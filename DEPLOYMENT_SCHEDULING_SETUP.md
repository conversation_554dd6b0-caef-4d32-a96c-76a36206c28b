# Environment-Specific Deployment Scheduling & Restrictions

This document explains the new environment-specific deployment scheduling functionality and environment restrictions that have been added to the DevOps Agent.

## Overview

The system now supports configuring deployment time windows for different environments. When a build is approved, the system checks if the current time falls within the allowed deployment window for that environment:

- **If within window**: Build is triggered immediately (status: `WAITING`)
- **If outside window**: Build is queued for later processing (status: `QUEUED`)

A scheduled job runs every 5 minutes to check queued builds and trigger them when their deployment window opens.

## Database Schema

### Environment Deployment Scheduling
A new table `ENVIRONMENT_DEPLOYMENT_CONFIG` has been added with the following structure:

```sql
CREATE TABLE ENVIRONMENT_DEPLOYMENT_CONFIG (
    ID INT PRIMARY KEY AUTO_INCREMENT,
    ENVIRONMENT VARCHAR(255) NOT NULL UNIQUE,
    DEPLOYMENT_START_TIME TIME,
    DEPLOYMENT_END_TIME TIME,
    SECONDARY_START_TIME TIME,
    SECONDARY_END_TIME TIME,
    IS_ACTIVE BOOLEAN DEFAULT TRUE,
    CREATED_DATE TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UPDATED_DATE TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### Environment Restrictions
A new table `ENVIRONMENT_RESTRICTION` has been added to replace hardcoded environment restrictions:

```sql
CREATE TABLE ENVIRONMENT_RESTRICTION (
    ID INT PRIMARY KEY AUTO_INCREMENT,
    ENVIRONMENT VARCHAR(255) NOT NULL,
    RESTRICTION_PATTERN VARCHAR(255),
    ALLOWED_APPROVERS TEXT,
    ENABLE_RESTRICTION BOOLEAN DEFAULT TRUE,
    RESTRICTION_MESSAGE TEXT,
    CREATED_DATE TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UPDATED_DATE TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## Configuration Examples

### Deployment Scheduling Examples

### Example 1: Single Time Window
For `humainos-tst` environment with deployment allowed between 3-4 PM:

```sql
INSERT INTO ENVIRONMENT_DEPLOYMENT_CONFIG 
(ENVIRONMENT, DEPLOYMENT_START_TIME, DEPLOYMENT_END_TIME, IS_ACTIVE) 
VALUES ('humainos-tst', '15:00:00', '16:00:00', TRUE);
```

### Example 2: Two Time Windows
For `humainos-tst` environment with deployment allowed between 3-4 PM and 9-10 PM:

```sql
INSERT INTO ENVIRONMENT_DEPLOYMENT_CONFIG 
(ENVIRONMENT, DEPLOYMENT_START_TIME, DEPLOYMENT_END_TIME, SECONDARY_START_TIME, SECONDARY_END_TIME, IS_ACTIVE) 
VALUES ('humainos-tst', '15:00:00', '16:00:00', '21:00:00', '22:00:00', TRUE);
```

### Example 3: Overnight Window
For deployment window that crosses midnight (11 PM to 1 AM):

```sql
INSERT INTO ENVIRONMENT_DEPLOYMENT_CONFIG 
(ENVIRONMENT, DEPLOYMENT_START_TIME, DEPLOYMENT_END_TIME, IS_ACTIVE) 
VALUES ('humainos-prod', '23:00:00', '01:00:00', TRUE);
```

### Environment Restriction Examples

#### Example 1: SAI Environment Restriction
Restrict SAI dev/sit environments to specific approvers:

```sql
INSERT INTO ENVIRONMENT_RESTRICTION
(ENVIRONMENT, RESTRICTION_PATTERN, ALLOWED_APPROVERS, ENABLE_RESTRICTION, RESTRICTION_MESSAGE)
VALUES (
    'sai-dev-sit',
    'sai',
    '<EMAIL>,<EMAIL>,<EMAIL>',
    TRUE,
    'Dear Team, SAI environments are under migration. <NAME_EMAIL>'
);
```

#### Example 2: Production Environment Restriction
Restrict production deployments to senior approvers:

```sql
INSERT INTO ENVIRONMENT_RESTRICTION
(ENVIRONMENT, RESTRICTION_PATTERN, ALLOWED_APPROVERS, ENABLE_RESTRICTION, RESTRICTION_MESSAGE)
VALUES (
    'production',
    'prod',
    '<EMAIL>,<EMAIL>,<EMAIL>',
    TRUE,
    'Production deployments require senior approval. Contact <EMAIL>'
);
```

## New Build Status

A new status `QUEUED` has been added to the system:

- **PENDING**: Waiting for approval
- **APPROVED**: Build completed successfully
- **ERROR**: Build failed or error occurred
- **WAITING**: Build is currently running
- **QUEUED**: Build is approved but waiting for deployment window (NEW)

## API Endpoints

### Check if deployment is allowed
```
GET /api/deployment-config/{environment}/is-allowed
```

### Get deployment configuration
```
GET /api/deployment-config/{environment}
```

### Create/Update deployment configuration
```
POST /api/deployment-config
Content-Type: application/json

{
    "environment": "humainos-tst",
    "deploymentStartTime": "15:00:00",
    "deploymentEndTime": "16:00:00",
    "secondaryStartTime": "21:00:00",
    "secondaryEndTime": "22:00:00",
    "isActive": true
}
```

## How It Works

### For Non-Production Builds:
1. **Email Processing**: When a build request email is received, it's processed normally
2. **Approval Process**: When an approver approves a build:
   - System checks if current time is within deployment window for the target environment
   - If YES: Build is triggered immediately (status: `WAITING`)
   - If NO: Build is saved with status `QUEUED`
3. **Queue Processing**: A scheduled job runs every 5 minutes to process queued builds

### For Production Builds (Two-Stage Process):
1. **Email Processing**: When a production build request email is received, it's processed normally
2. **Approval Process**: When an approver approves a production build:
   - **First job is ALWAYS triggered immediately** (runs in dev environment)
   - Production build record is created with status `PENDING`
3. **First Job Completion**: When the first job completes successfully:
   - System checks if current time is within deployment window for production environment
   - If YES: Second job (production deployment) is triggered immediately (status: `WAITING`)
   - If NO: Production build is saved with status `QUEUED`
4. **Queue Processing**: A scheduled job runs every 5 minutes:
   - Finds all production builds with status `QUEUED`
   - Checks if deployment window is now open for production environment
   - Triggers second job (production deployment) when window opens
   - Updates status from `QUEUED` to `WAITING`

This approach ensures that for production builds:
- **First job runs immediately** to save time and validate the build
- **Second job (actual deployment) respects the deployment window**

## Scheduled Jobs

The system now has these scheduled jobs:

1. **pollBuildEmail()**: Every 2 minutes - Poll for new emails
2. **checkProductionBuildStatus()**: Every 1 minute - Check production build status
3. **checkBuildStatus()**: Every 1 minute - Check regular build status
4. **processQueuedBuilds()**: Every 5 minutes - Process queued builds (NEW)

## Configuration Management

### Via Database
Direct SQL inserts/updates to `ENVIRONMENT_DEPLOYMENT_CONFIG` table.

### Via REST API
Use the provided REST endpoints to manage configurations programmatically.

### Default Behavior
If no configuration exists for an environment, deployments are allowed at any time (backward compatibility).

## Logging

The system logs deployment window checks and queue processing:

```
INFO  - Deployment not allowed at current time for environment: humainos-tst. Queueing build for later processing.
INFO  - Deployment window is now open for environment: humainos-tst. Processing queued build.
INFO  - Successfully triggered queued build for application: myapp in environment: humainos-tst
```

## Production Build Flow Details

### Two-Stage Production Pipeline:
1. **Stage 1 (Dev Environment)**: Always runs immediately when approved
   - Validates the build
   - Runs tests and compilation
   - Creates artifacts
   - **Not affected by deployment windows**

2. **Stage 2 (Production Environment)**: Respects deployment windows
   - Actual deployment to production
   - Only runs during allowed time windows
   - Can be queued if outside deployment window

### Example Scenario:
- Production build approved at 2:00 PM
- Deployment window: 3:00 PM - 4:00 PM
- **Result**:
  - Stage 1 runs immediately at 2:00 PM
  - Stage 2 is queued and runs at 3:00 PM when window opens

## Testing

### For Non-Production Builds:
1. Configure a deployment window for an environment (e.g., staging)
2. Submit a build request for that environment outside the window
3. Approve the build - it should be queued
4. Wait for the deployment window to open
5. The queued build should be automatically triggered

### For Production Builds:
1. Configure a deployment window for a production environment
2. Submit a production build request outside the window
3. Approve the build:
   - First job should run immediately
   - Production build should be created with QUEUED status
4. Wait for the deployment window to open
5. The queued production deployment should be automatically triggered

## Backward Compatibility

The system maintains full backward compatibility:
- Environments without deployment configurations work as before
- Existing build statuses and flows are unchanged
- Only adds new functionality without breaking existing features
