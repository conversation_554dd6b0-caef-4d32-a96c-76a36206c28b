# Use lightweight Alpine-based JDK image
FROM registry.visionwaves.com/alpine-fixed:3.20.3

# Install required tools
RUN apk add --no-cache \
    procps \
    bash \
    curl \
    ttf-dejavu && \
    rm -rf /var/cache/apk/*

# Set environment variables
ENV LANG=en_US.UTF-8 \
    LANGUAGE=en_US:en \
    LC_ALL=en_US.UTF-8 \
    SERVICE_ARCHIVE=devops-agent \
    BASE_PATH=/opt/visionwaves/devops-agent \
    SERVICE_PATH=/opt/visionwaves/devops-agent

ENV E_C=SWjZ0G5at8kSkWenV2z3VA==:aw/3tKfaV6GdeL6T2+0wMw==:lK4XYk8Mmg2XzxVVsHbJpD7fG1gdqk5BODwsNEWphjk=

# Create user and directory
RUN addgroup -S visionwaves && adduser -S -G visionwaves visionwaves && \
    mkdir -p "$BASE_PATH" && \
    chown -R visionwaves:visionwaves "$BASE_PATH"

# Get the APP_NAME from build arg
ARG APP_NAME

# Extract the tar file
COPY ${APP_NAME}.tar /tmp/
RUN mkdir -p /tmp/extract && \
    tar -xf /tmp/${APP_NAME}.tar -C /tmp/extract && \
    cp /tmp/extract/devops-agent-1.0.0.jar $BASE_PATH/app.jar && \
    cp /tmp/extract/run.sh $BASE_PATH/ && \
    cp /tmp/extract/multi-env-truststore.jks $BASE_PATH/ && \
    chmod +x $BASE_PATH/run.sh && \
    chown -R visionwaves:visionwaves $BASE_PATH && \
    rm -rf /tmp/${APP_NAME}.tar /tmp/extract

# Set permissions and switch to non-root user
USER visionwaves

# Set working directory
WORKDIR $SERVICE_PATH

# Expose port (if applicable)
EXPOSE 8080

# Run the Spring Boot app using the run.sh script
CMD ["./run.sh"]

