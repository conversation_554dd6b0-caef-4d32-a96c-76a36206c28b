#!/bin/bash

# Exit on any error
set -e

# Check for version argument
if [ -z "$1" ]; then
    echo "Error: Version argument is required."
    echo "Usage: $0 <version>"
    exit 1
fi

# Build the project (skip tests, skip proguard, skip OWASP dependency check)
mvn clean install -DskipTests=true -Dproguard.skip=true -Ddependency-check.skip=true

# Go to the target directory
cd ./target/

# Copy the run.sh and Dockerfile to the target directory if they don't exist
if [ ! -f "run.sh" ]; then
    cp ../run.sh .
    chmod +x run.sh
fi

if [ ! -f "Dockerfile" ]; then
    cp ../Dockerfile .
fi

if [ ! -f "multi-env-truststore.jks" ]; then
    cp ../multi-env-truststore.jks .
fi

# Package the build into a tar archive
tar -czf devops-agent-build.tar devops-agent-1.0.0.jar run.sh multi-env-truststore.jks

# Build and push the Docker image
docker build --platform=linux/amd64 -t registry.visionwaves.com/devops-agent:"$1" --build-arg APP_NAME=devops-agent-build . --no-cache
docker push registry.visionwaves.com/devops-agent:"$1"

