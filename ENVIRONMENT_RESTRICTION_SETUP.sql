-- Environment Restriction Setup Script
-- This script creates the ENVIRONMENT_RESTRICTION table and sample data

-- Create the table
CREATE TABLE ENVIRONMENT_RESTRICTION (
    ID INT PRIMARY KEY AUTO_INCREMENT,
    ENVIRONMENT VARCHAR(255) NOT NULL,
    RESTRICTION_PATTERN VARCHAR(255),
    ALLOWED_APPROVERS TEXT,
    ENABLE_RESTRICTION BOOLEAN DEFAULT TRUE,
    RESTRICTION_MESSAGE TEXT,
    CREATED_DATE TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UPDATED_DATE TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Sample data for SAI environment restrictions
-- This replaces the hardcoded saiDevApproval logic

-- SAI Dev/Sit Environment Restriction
INSERT INTO ENVIRONMENT_RESTRICTION 
(ENVIRONMENT, RESTRICTION_PATTERN, ALLOWED_APPROVERS, ENA<PERSON>E_RESTRICTION, RESTRICTION_MESSAGE) 
VALUES (
    'sai-dev-sit',
    'sai',
    '<EMAIL>,<EMAIL>,<EMAIL>',
    TRUE,
    'Dear Team,

We are currently in the process of migrating the sai-dev and sai-sit environment to a new infrastructure. As part of this transition, automated deployment requests sent for sai-dev and sai-sit will be temporarily paused.

Until the migration is complete, we request that any deployment to the sai-dev environment be submitted only to:

📩 <EMAIL>

Best regards,
AI DevOps (Visionwaves)
Where automation comes first!!!'
);

-- Example: Production environment restriction (only specific approvers)
INSERT INTO ENVIRONMENT_RESTRICTION 
(ENVIRONMENT, RESTRICTION_PATTERN, ALLOWED_APPROVERS, ENABLE_RESTRICTION, RESTRICTION_MESSAGE) 
VALUES (
    'production',
    'prod',
    '<EMAIL>,<EMAIL>,<EMAIL>',
    FALSE, -- Disabled by default
    'Dear Team,

Production deployments require special approval. Please contact the senior DevOps team:

📩 <EMAIL>

Best regards,
AI DevOps (Visionwaves)
Where automation comes first!!!'
);

-- Example: Staging environment restriction during maintenance
INSERT INTO ENVIRONMENT_RESTRICTION 
(ENVIRONMENT, RESTRICTION_PATTERN, ALLOWED_APPROVERS, ENABLE_RESTRICTION, RESTRICTION_MESSAGE) 
VALUES (
    'staging',
    'staging',
    '<EMAIL>',
    FALSE, -- Disabled by default
    'Dear Team,

Staging environment is currently under maintenance. Deployments are temporarily restricted.

Please contact the DevOps team:

📩 <EMAIL>

Best regards,
AI DevOps (Visionwaves)
Where automation comes first!!!'
);

-- Query to check current restrictions
-- SELECT * FROM ENVIRONMENT_RESTRICTION WHERE ENABLE_RESTRICTION = TRUE;

-- Query to enable/disable restrictions
-- UPDATE ENVIRONMENT_RESTRICTION SET ENABLE_RESTRICTION = TRUE WHERE ENVIRONMENT = 'sai-dev-sit';
-- UPDATE ENVIRONMENT_RESTRICTION SET ENABLE_RESTRICTION = FALSE WHERE ENVIRONMENT = 'sai-dev-sit';

-- Query to update allowed approvers
-- UPDATE ENVIRONMENT_RESTRICTION 
-- SET ALLOWED_APPROVERS = '<EMAIL>,<EMAIL>,<EMAIL>' 
-- WHERE ENVIRONMENT = 'sai-dev-sit';

-- Query to update restriction message
-- UPDATE ENVIRONMENT_RESTRICTION 
-- SET RESTRICTION_MESSAGE = 'Your custom restriction message here...' 
-- WHERE ENVIRONMENT = 'sai-dev-sit';
