spring.application.name=devops-agent

server.port=8088
server.servlet.context-path=/devops-agent/rest

## Logging configuration
logging.level.root=ERROR
logging.level.com.enttribe.devops_agent=DEBUG
logging.level.com.enttribe.commons.ai=DEBUG
spring.output.ansi.enabled=ALWAYS

## Spring AI configuration
spring.ai.openai.chat.api-key=********************************************************
spring.ai.openai.chat.base-url=https://api.groq.com/openai/
spring.ai.openai.chat.options.model=llama-3.3-70b-versatile

## SDK configuration
commons.ai.sdk.is_local=${IS_LOCAL:true}
commons.ai.sdk.app.name=DEVOPS_SINGULARITY_APP_NAME
exception.audit.enable=true
prompt.audit.enable=true

## Database configuration
spring.datasource.url=${MYSQL_URL:****************************************}
spring.datasource.username=${MYSQL_USERNAME:root}
spring.datasource.password=${MYSQL_CHECKSUM:root}
spring.jpa.hibernate.ddl-auto=none
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl

should.poll=${SHOULD_POLL:false}

##build-email
build.approval.list=<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
email.body.content.token.limit=8192
