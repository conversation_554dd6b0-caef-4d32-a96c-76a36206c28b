# Email configuration
email:
  success-draft: classpath:draft/build_success.st
  failed-draft: classpath:draft/build_fail.st
  format-issue: classpath:draft/format_issue.st
  approvers:
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
    - <EMAIL>
  limited-deployment-template: |
    Dear Team member,
    
    We received your deployment request; however, it exceeds the allowed limit of deployments per day for your application. 
    To ensure system stability and fair resource utilization, we cannot process additional deployments beyond this limit.
    
    If your request is urgent, please reach out to DevOps Team to discuss possible exceptions. 
    Otherwise, you may resubmit your request on the next business day.
    
    Thank you for your understanding. Let us know if you have any questions.
    
    Best Regards,
    AI Devops(visionwaves)
    Where automation comes first!!!
  unauthorized-template: |
    Dear Team member,
    
    We received your request to approve a deployment, but according to our policy, 
    only authorized personnel are permitted to approve pipeline executions. 
    Unfortunately, you are not listed as an authorized approver, and we are unable to process your request.
    
    If this approval was made in error, please inform the designated approver within your team. 
    If you believe you should be authorized, kindly reach out to Mr. Shiv Chandra Pathak(<EMAIL>) for further clarification.
    
    Thank you for your cooperation.
    
    Best Regards,
    AI Devops(visionwaves)
    Where automation comes first!!!
  unexpected-error-template: |
    Dear team,
    
    Your request could not be processed due to an unexpected error.
    
    Please contact Devops team or initiate another request.
    
    Request id : %s
    
    Best Regards,
    AI Devops(visionwaves)
    Where automation comes first!!!

# Jenkins configuration
jenkins:
  email-id: <EMAIL>
  supported-environments:
    - dev
    - demo
    - humainos-dev
    - humainos-tst
    - sai-demo
    - sai-prod
    - hos-dev
    - hos-tst
    - project-singularity
  environments:
    dev:
      jenkins-url: https://dev.visionwaves.com/jenkins
      frontend-job-name: MicroFrontend_Dev_angular_17
      backend-job-name: Backend_Microservice_Dev_java_21
      shell-app-job-name: Shell-App_Dev
      keycloak-job-name: keycloak-Backend_Microservice_Dev_java_21
      jenkins-user: admin
      jenkins-token: 118694c146daa45078e246b554113c3801
    demo:
      jenkins-url: https://dev.visionwaves.com/jenkins
      frontend-job-name: MicroFrontend_Demo_angular_17
      backend-job-name: Backend_Microservice_Demo_java_21
      shell-app-job-name: Shell-App_Demo
      keycloak-job-name: keycloak-Backend_Microservice_Demo_java_21
      jenkins-user: admin
      jenkins-token: 118694c146daa45078e246b554113c3801
    humainos-dev:
      jenkins-url: http://jenkins-hos-dev.thefutureai.local/
      frontend-job-name: MicroFrontend_Cd_Pipeline
      backend-job-name: Backend_Microservice_Cd_Pipeline
      shell-app-job-name: Shell_Cd_Pipeline
      keycloak-job-name: Backend_Microservice_Cd_Pipeline
      jenkins-user: faizan-shah
      jenkins-token: 11ba28fab4d8c6a38f08ba090fcecc82c2
    humainos-tst:
      jenkins-url: https://humainos-sit.thefutureai.local/jenkins/
      frontend-job-name: MicroFrontend_Cd_Pipeline
      backend-job-name: Backend_Microservice_Cd_Pipeline
      shell-app-job-name: Shell_Cd_Pipeline
      keycloak-job-name: Backend_Microservice_Cd_Pipeline
      jenkins-user: developer
      jenkins-token: 11ad8fa58df627e1bf0cbad0a8fd5f9e43
    sai-demo:
      jenkins-url: https://humainos-demo.thefutureai.co/jenkins/
      frontend-job-name: MicroFrontend_Cd_Pipeline
      backend-job-name: Backend_Microservice_Cd_Pipeline
      shell-app-job-name: Shell_Cd_Pipeline
      keycloak-job-name: Backend_Microservice_Cd_Pipeline
      jenkins-user: developer
      jenkins-token: 1112ad884d73422b474b9fdbb8ae376227
    sai-prod:
      jenkins-url: https://humainos.thefutureai.co/jenkins/
      frontend-job-name: MicroFrontend_Cd_Pipeline
      backend-job-name: Backend_Microservice_Cd_Pipeline
      shell-app-job-name: Shell_Cd_Pipeline
      keycloak-job-name: Backend_Microservice_Cd_Pipeline
      jenkins-user: developer
      jenkins-token: 116c0ed9eaf87e951c2f58af25cb8b747e
    hos-dev:
      jenkins-url: https://marketplace-dev.thefutureai.co/jenkins/
      frontend-job-name: MicroFrontend_Cd_Pipeline
      backend-job-name: Backend_Microservice_Cd_Pipeline
      shell-app-job-name: Shell_Cd_Pipeline
      keycloak-job-name: Backend_Microservice_Cd_Pipeline
      jenkins-user: developer
      jenkins-token: 11f32503552f1203b78fe839134b1f0edd
    hos-tst:
      jenkins-url: https://marketplace-sit.thefutureai.co/jenkins/
      frontend-job-name: MicroFrontend_Cd_Pipeline
      backend-job-name: Backend_Microservice_Cd_Pipeline
      shell-app-job-name: Shell_Cd_Pipeline
      keycloak-job-name: Backend_Microservice_Cd_Pipeline
      jenkins-user: developer
      jenkins-token: 1135751f50a48f8bbe31d804cd416052f2
    project-singularity:
      jenkins-url: https://project-singularity-ab6d67c990e9c01a.elb.ap-south-1.amazonaws.com/jenkins/
      frontend-job-name: MicroFrontend_Cd_Pipeline
      backend-job-name: Backend_Microservice_Cd_Pipeline
      shell-app-job-name: Shell_Cd_Pipeline
      keycloak-job-name: Backend_Microservice_Cd_Pipeline
      jenkins-user: projectJenkins
      jenkins-token: 112d30fcbf97c5c48ba85c44a1d4652565
