package com.enttribe.devops_agent.rest;

import com.enttribe.devops_agent.dto.ChatHistoryDto;
import com.enttribe.devops_agent.entity.ChatMessage;
import com.enttribe.devops_agent.service.ChatHistoryService;
import com.enttribe.devops_agent.service.K8sService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequiredArgsConstructor
public class K8sRest {

    private static final Logger log = LoggerFactory.getLogger(K8sRest.class);
    private final K8sService k8sService;
    private final ChatHistoryService chatHistoryService;

    @GetMapping("/ping")
    public Map<String, String> success() {
//        log.info("Health check endpoint called.");
        return Map.of("status", "success");
    }

    @PostMapping("/executeK8sAgent")
    public String executeK8sAgent(@RequestBody ChatHistoryDto dto, @RequestHeader(name = "api-key", required = false, defaultValue = "") String apiKey) {
//        if (!apiKey.equals("not-to-be-used-by-everyone")) return "not-allowed";

        return k8sService.executeK8sAgent(dto);
    }

    @GetMapping("/getChatHistory")
    List<ChatMessage> getChatHistory(@RequestParam(name = "conversationId") String conversationId) {
        return chatHistoryService.getChatHistory(conversationId);
    }

}
