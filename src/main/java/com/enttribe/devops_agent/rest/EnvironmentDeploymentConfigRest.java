package com.enttribe.devops_agent.rest;

import com.enttribe.devops_agent.entity.EnvironmentDeploymentConfig;
import com.enttribe.devops_agent.service.EnvironmentDeploymentConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalTime;
import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing environment deployment configurations.
 * Provides endpoints to configure deployment time windows for different environments.
 */
@RestController
@RequestMapping("/api/deployment-config")
@RequiredArgsConstructor
@Slf4j
public class EnvironmentDeploymentConfigRest {

    private final EnvironmentDeploymentConfigService environmentDeploymentConfigService;

    /**
     * Get all deployment configurations
     */
    @GetMapping
    public ResponseEntity<List<EnvironmentDeploymentConfig>> getAllConfigurations() {
        // This would need to be implemented in the service
        return ResponseEntity.ok().build();
    }

    /**
     * Get deployment configuration for a specific environment
     */
    @GetMapping("/{environment}")
    public ResponseEntity<EnvironmentDeploymentConfig> getConfiguration(@PathVariable String environment) {
        Optional<EnvironmentDeploymentConfig> config = environmentDeploymentConfigService.getDeploymentConfig(environment);
        return config.map(ResponseEntity::ok).orElse(ResponseEntity.notFound().build());
    }

    /**
     * Create or update deployment configuration for an environment
     */
    @PostMapping
    public ResponseEntity<EnvironmentDeploymentConfig> saveConfiguration(@RequestBody EnvironmentDeploymentConfigRequest request) {
        try {
            EnvironmentDeploymentConfig config = EnvironmentDeploymentConfig.builder()
                    .environment(request.getEnvironment())
                    .deploymentStartTime(request.getDeploymentStartTime())
                    .deploymentEndTime(request.getDeploymentEndTime())
                    .secondaryStartTime(request.getSecondaryStartTime())
                    .secondaryEndTime(request.getSecondaryEndTime())
                    .isActive(request.getIsActive() != null ? request.getIsActive() : true)
                    .build();

            EnvironmentDeploymentConfig savedConfig = environmentDeploymentConfigService.saveDeploymentConfig(config);
            return ResponseEntity.ok(savedConfig);
        } catch (Exception e) {
            log.error("Error saving deployment configuration: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Check if deployment is currently allowed for an environment
     */
    @GetMapping("/{environment}/is-allowed")
    public ResponseEntity<DeploymentStatusResponse> isDeploymentAllowed(@PathVariable String environment) {
        boolean isAllowed = environmentDeploymentConfigService.isDeploymentAllowed(environment);
        return ResponseEntity.ok(new DeploymentStatusResponse(environment, isAllowed));
    }

    /**
     * Request DTO for deployment configuration
     */
    public static class EnvironmentDeploymentConfigRequest {
        private String environment;
        private LocalTime deploymentStartTime;
        private LocalTime deploymentEndTime;
        private LocalTime secondaryStartTime;
        private LocalTime secondaryEndTime;
        private Boolean isActive;

        // Getters and setters
        public String getEnvironment() { return environment; }
        public void setEnvironment(String environment) { this.environment = environment; }
        
        public LocalTime getDeploymentStartTime() { return deploymentStartTime; }
        public void setDeploymentStartTime(LocalTime deploymentStartTime) { this.deploymentStartTime = deploymentStartTime; }
        
        public LocalTime getDeploymentEndTime() { return deploymentEndTime; }
        public void setDeploymentEndTime(LocalTime deploymentEndTime) { this.deploymentEndTime = deploymentEndTime; }
        
        public LocalTime getSecondaryStartTime() { return secondaryStartTime; }
        public void setSecondaryStartTime(LocalTime secondaryStartTime) { this.secondaryStartTime = secondaryStartTime; }
        
        public LocalTime getSecondaryEndTime() { return secondaryEndTime; }
        public void setSecondaryEndTime(LocalTime secondaryEndTime) { this.secondaryEndTime = secondaryEndTime; }
        
        public Boolean getIsActive() { return isActive; }
        public void setIsActive(Boolean isActive) { this.isActive = isActive; }
    }

    /**
     * Response DTO for deployment status
     */
    public static class DeploymentStatusResponse {
        private String environment;
        private boolean isAllowed;

        public DeploymentStatusResponse(String environment, boolean isAllowed) {
            this.environment = environment;
            this.isAllowed = isAllowed;
        }

        // Getters
        public String getEnvironment() { return environment; }
        public boolean isAllowed() { return isAllowed; }
    }
}
