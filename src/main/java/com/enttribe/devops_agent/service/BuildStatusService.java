package com.enttribe.devops_agent.service;

import com.enttribe.devops_agent.config.EmailTemplateConfig;
import com.enttribe.devops_agent.config.JenkinsConfig;
import com.enttribe.devops_agent.constants.BuildConstants;
import com.enttribe.devops_agent.dao.BuildApplicationDao;
import com.enttribe.devops_agent.dao.BuildControlDao;
import com.enttribe.devops_agent.dao.ProductionBuildDao;
import com.enttribe.devops_agent.entity.BuildApplication;
import com.enttribe.devops_agent.entity.BuildControl;
import com.enttribe.devops_agent.entity.BuildEmails;
import com.enttribe.devops_agent.entity.ProductionBuild;
import com.enttribe.devops_agent.util.AIUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.stereotype.Service;

import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
@RequiredArgsConstructor
public class BuildStatusService {

    private final BuildControlDao buildControlDao;
    private final BuildApplicationDao buildApplicationDao;
    private final ProductionBuildDao productionBuildDao;
    private final MailService mailService;
    private final JenkinsService jenkinsService;
    private final EmailTemplateConfig emailTemplateConfig;
    private final JenkinsConfig jenkinsConfig;
    private final BuildEmailProcessingService emailProcessingService;
    private final EnvironmentDeploymentConfigService environmentDeploymentConfigService;

    /**
     * Process a successful build
     */
    public void processSuccessfulBuild(BuildEmails buildEmail, String environment, Map<String, Object> variableMap) {
        String emailId = jenkinsConfig.getEmailId();

        try {
            // Update build status
            buildEmail.setStatus(BuildConstants.APPROVED);
            buildEmail.setDeployedDate(new Date());

            // Send confirmation email
            String draft = AIUtils.getResolvedPrompt(emailTemplateConfig.getSuccessDraft(), variableMap);
            mailService.replyBuildEmail(emailId, buildEmail.getReplyTo(), draft);

            // Update deployment counts
            updateDeploymentCounts(buildEmail.getApplicationName(), environment, true, buildEmail.getSubject());
        } catch (Exception e) {
            log.error("Error processing successful build", e);
            buildEmail.setStatus(BuildConstants.ERROR);
            buildEmail.setError(e.getMessage());
            emailProcessingService.sendUnexpectedErrorMail(buildEmail.getMessageId(), buildEmail.getInternetMessageId());
        }
    }

    /**
     * Process a failed build
     */
    public void processFailedBuild(BuildEmails buildEmail, String buildStatus, String environment, Map<String, Object> variableMap) {
        String emailId = jenkinsConfig.getEmailId();

        try {
            // Update build status
            buildEmail.setStatus(BuildConstants.ERROR);

            // Send failure email with logs
            String draft = AIUtils.getResolvedPrompt(emailTemplateConfig.getFailedDraft(), variableMap);
            BufferedInputStream attachmentStream = new BufferedInputStream(
                    new ByteArrayInputStream(buildStatus.getBytes(StandardCharsets.UTF_8)));
            mailService.replyWithAttachment(emailId, buildEmail.getReplyTo(), draft, attachmentStream, "pipeline_error.log");

            // Update deployment counts
            updateDeploymentCounts(buildEmail.getApplicationName(), environment, false, buildEmail.getSubject());
        } catch (Exception e) {
            log.error("Error processing failed build", e);
            buildEmail.setStatus(BuildConstants.ERROR);
            buildEmail.setError(e.getMessage());
            emailProcessingService.sendUnexpectedErrorMail(buildEmail.getMessageId(), buildEmail.getInternetMessageId());
        }
    }

    /**
     * Create a production build record
     * Note: Always creates with PENDING status. Deployment window check happens when first job completes.
     */
    public void createProductionBuild(BuildEmails buildEmail) {
        ProductionBuild productionBuild = ProductionBuild.builder()
                .buildEmail(buildEmail)
                .internetMessageId(buildEmail.getInternetMessageId())
                .approvedBy(buildEmail.getApprovedBy())
                .replyTo(buildEmail.getReplyTo())
                .createdTime(new Date())
                .status(BuildConstants.PENDING)
                .buildParameters(buildEmail.getBuildParameters())
                .applicationName(buildEmail.getApplicationName())
                .build();

        productionBuildDao.save(productionBuild);
    }

    /**
     * Update deployment counts
     */
    private void updateDeploymentCounts(String applicationName, String environment, boolean isSuccess, String body) {
        String type = body.toLowerCase().contains("frontend") ? "frontend" : "backend";
        List<BuildControl> deployments = buildControlDao.findByApplicationNameAndDeploymentDate(
                applicationName, LocalDate.now(ZoneId.of("Asia/Kolkata")), environment, type);

        if (deployments.size() > 1) {
            // Multiple deployments found - this is unusual
            String subject = "Multiple records were found for the same application";
            String content = String.format("""
                    Dear Deepak,
                    
                    Multiple records were found for the same application.
                    Application name: %s
                    Environment: %s
                    
                    Best Regards,
                    AI Devops(visionwaves)
                    Where automation comes first!!!
                    """, applicationName, environment);

            mailService.sendMail(jenkinsConfig.getEmailId(), subject, content,
                    "<EMAIL>", null, null, true);
        } else if (deployments.size() == 1) {
            // Update existing deployment record
            BuildControl deployment = deployments.getFirst();
            if (isSuccess) {
                int successCount = deployment.getSuccessCount() == null ? 0 : deployment.getSuccessCount();
                deployment.setSuccessCount(successCount + 1);
            } else {
                int failedCount = deployment.getFailedCount() == null ? 0 : deployment.getFailedCount();
                deployment.setFailedCount(failedCount + 1);
            }
            deployment.setEnvironment(environment);
            deployment.setType(type);
            buildControlDao.save(deployment);
        } else {
            // Create new deployment record
            BuildControl buildControl = new BuildControl();
            buildControl.setApplicationName(applicationName);
            if (isSuccess) {
                buildControl.setSuccessCount(1);
                buildControl.setFailedCount(0);
            } else {
                buildControl.setSuccessCount(0);
                buildControl.setFailedCount(1);
            }
            buildControl.setEnvironment(environment);
            buildControl.setDeploymentDate(LocalDate.now(ZoneId.of("Asia/Kolkata")));
            buildControl.setType(type);
            buildControlDao.save(buildControl);
        }
    }

    /**
     * Check if deployment limit is reached
     */
    public boolean isDeploymentLimitReached(String applicationName, String environment, String subject) {
        String type = subject.toLowerCase().contains("frontend") ? "frontend" : "backend";

        Integer successfulDeploymentCount = buildControlDao.findSuccessDeploymentCountByDate(
                applicationName, LocalDate.now(ZoneId.of("Asia/Kolkata")), environment, type);

        if (successfulDeploymentCount == null) {
            successfulDeploymentCount = 0;
        }

        BuildApplication applicationDetail = buildApplicationDao.findByApplicationName(applicationName, environment, type);
        int allowedDeployment;

        if (applicationDetail == null || applicationDetail.getAllowedDeployment() == null) {
            log.warn("No application detail found for {}. Creating one with default limit.", applicationName);
            allowedDeployment = 2; // Default limit

            BuildApplication buildApplication = new BuildApplication();
            buildApplication.setApplicationName(applicationName);
            buildApplication.setEnvironment(environment);
            buildApplication.setType(type);
            buildApplication.setAllowedDeployment(allowedDeployment);
            buildApplicationDao.save(buildApplication);
        } else {
            allowedDeployment = applicationDetail.getAllowedDeployment();
        }

        log.debug("Allowed deployment: {} for application: {}", allowedDeployment, applicationName);
        return successfulDeploymentCount >= allowedDeployment;
    }

    /**
     * Create build variable map for templates
     */
    public Map<String, Object> createBuildVariableMap(BuildEmails buildEmail, String buildUrl) {
        JSONObject buildParams = buildEmail.getBuildParameters() == null ?
                new JSONObject() : new JSONObject(buildEmail.getBuildParameters());

        String applicationName = buildParams.optString("APPLICATION_NAME");
        String branch = buildParams.optString("GIT_BRANCH");
        String gitTag = buildParams.optString("GIT_TAG");
        String env = buildParams.optString("ENV");
        String buildNumber = jenkinsService.extractBuildNumber(buildUrl);

        Map<String, Object> variableMap = new HashMap<>();
        variableMap.put("APPLICATION_NAME", applicationName);
        variableMap.put("ENV", env);
        variableMap.put("GIT_TAG", gitTag);
        variableMap.put("BRANCH", branch);
        variableMap.put("BUILD_NUMBER", buildNumber);
        variableMap.put("MAIN_JS", buildEmail.getMainJs() != null ? buildEmail.getMainJs() : "---");
        return variableMap;
    }

}
