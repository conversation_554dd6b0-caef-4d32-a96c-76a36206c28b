package com.enttribe.devops_agent.service;

import com.enttribe.devops_agent.dao.EnvironmentDeploymentConfigDao;
import com.enttribe.devops_agent.entity.EnvironmentDeploymentConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalTime;
import java.util.Optional;

/**
 * Service for managing environment deployment configurations.
 * Handles deployment time window logic and validation.
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class EnvironmentDeploymentConfigService {

    private final EnvironmentDeploymentConfigDao environmentDeploymentConfigDao;

    /**
     * Check if the current time is within the deployment window for the given environment
     *
     * @param environment The environment to check
     * @return true if deployment is allowed at current time, false otherwise
     */
    public boolean isDeploymentAllowed(String environment) {
        Optional<EnvironmentDeploymentConfig> configOpt = environmentDeploymentConfigDao.findByEnvironment(environment);
        
        if (configOpt.isEmpty()) {
            log.debug("No deployment configuration found for environment: {}. Allowing deployment.", environment);
            return true; // If no configuration exists, allow deployment
        }

        EnvironmentDeploymentConfig config = configOpt.get();
        LocalTime currentTime = LocalTime.now();
        
        log.debug("Checking deployment window for environment: {} at time: {}", environment, currentTime);
        
        // Check primary deployment window
        if (isTimeInWindow(currentTime, config.getDeploymentStartTime(), config.getDeploymentEndTime())) {
            log.debug("Current time is within primary deployment window for environment: {}", environment);
            return true;
        }
        
        // Check secondary deployment window if configured
        if (config.getSecondaryStartTime() != null && config.getSecondaryEndTime() != null) {
            if (isTimeInWindow(currentTime, config.getSecondaryStartTime(), config.getSecondaryEndTime())) {
                log.debug("Current time is within secondary deployment window for environment: {}", environment);
                return true;
            }
        }
        
        log.debug("Current time is outside deployment windows for environment: {}", environment);
        return false;
    }

    /**
     * Check if a time falls within a given time window
     *
     * @param currentTime The time to check
     * @param startTime   The start of the window
     * @param endTime     The end of the window
     * @return true if the time is within the window
     */
    private boolean isTimeInWindow(LocalTime currentTime, LocalTime startTime, LocalTime endTime) {
        if (startTime == null || endTime == null) {
            return false;
        }
        
        // Handle case where window crosses midnight (e.g., 23:00 to 01:00)
        if (startTime.isAfter(endTime)) {
            return currentTime.isAfter(startTime) || currentTime.isBefore(endTime) || 
                   currentTime.equals(startTime) || currentTime.equals(endTime);
        } else {
            return (currentTime.isAfter(startTime) || currentTime.equals(startTime)) && 
                   (currentTime.isBefore(endTime) || currentTime.equals(endTime));
        }
    }

    /**
     * Get deployment configuration for an environment
     *
     * @param environment The environment name
     * @return Optional containing the configuration if found
     */
    public Optional<EnvironmentDeploymentConfig> getDeploymentConfig(String environment) {
        return environmentDeploymentConfigDao.findByEnvironment(environment);
    }

    /**
     * Save or update deployment configuration
     *
     * @param config The configuration to save
     * @return The saved configuration
     */
    public EnvironmentDeploymentConfig saveDeploymentConfig(EnvironmentDeploymentConfig config) {
        log.info("Saving deployment configuration for environment: {}", config.getEnvironment());
        return environmentDeploymentConfigDao.save(config);
    }

    /**
     * Check if deployment configuration exists for an environment
     *
     * @param environment The environment name
     * @return true if configuration exists
     */
    public boolean hasDeploymentConfig(String environment) {
        return environmentDeploymentConfigDao.existsByEnvironment(environment);
    }
}
