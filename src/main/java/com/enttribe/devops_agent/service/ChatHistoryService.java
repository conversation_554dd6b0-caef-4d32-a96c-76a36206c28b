package com.enttribe.devops_agent.service;

import com.enttribe.devops_agent.dao.ChatHistoryDao;
import com.enttribe.devops_agent.dao.ChatMessageDao;
import com.enttribe.devops_agent.dto.ChatHistoryDto;
import com.enttribe.devops_agent.entity.ChatHistory;
import com.enttribe.devops_agent.entity.ChatMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Service class for managing chat history.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ChatHistoryService {

    private final ChatHistoryDao historyDao;
    private final ChatMessageDao chatMessageDao;

    /**
     * Retrieves the chat history for a given process group ID.
     *
     * @param conversationId The process group ID.
     * @return An Optional containing the ChatHistory if found, or an empty Optional otherwise.
     */
    public List<ChatMessage> getChatHistory(String conversationId) {
        return chatMessageDao.getChatHistory(conversationId);
        }

        /**
         * Saves the chat history to the database.
         *
         * @param content The content of the chat message.
         * @param dto     The ChatHistoryDto containing the user query and conversation ID.
         */
        public void saveChatHistory(String content, ChatHistoryDto dto) {
            // Determine conversationId (generate if missing)
            String conversationId = Optional.ofNullable(dto.getConversationId())
                    .filter(id -> !id.isEmpty())
                    .orElseGet(() -> {
                        String generated = UUID.randomUUID().toString();
                        log.info("Generated new conversationId={}", generated);
                        return generated;
                    });

            // 🔥 Add this line to ensure dto gets updated with conversationId
            dto.setConversationId(conversationId);

            // Fetch or create ChatHistory
            ChatHistory history = historyDao.getChatHistory(conversationId)
                    .orElseGet(() -> {
                        log.info("Creating new ChatHistory for conversationId={}", conversationId);
                        ChatHistory newHistory = new ChatHistory();
                        newHistory.setConversationId(conversationId);
                        return newHistory;
                    });

            // 🔥 Log and Save ChatHistory
            historyDao.save(history);
            log.info("Saved  conversationId={}", history.getConversationId());

            // Create and save the ChatMessage
            ChatMessage chatMessage = new ChatMessage();
            chatMessage.setUserQuery(dto.getUserQuery());
            chatMessage.setAssistantResponse(content);
            chatMessage.setConversationId(conversationId);
            chatMessage.setStartTime(LocalDateTime.now());
            chatMessageDao.save(chatMessage);

            log.info("ChatMessage saved successfully for conversationId: {}", conversationId);
        }

}

