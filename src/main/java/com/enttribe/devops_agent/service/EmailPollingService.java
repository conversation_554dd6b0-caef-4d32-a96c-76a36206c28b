package com.enttribe.devops_agent.service;

import com.enttribe.devops_agent.config.JenkinsConfig;
import com.enttribe.devops_agent.dto.UserEmailDto;
import com.enttribe.devops_agent.dto.UserEmailResponseDto;
import com.enttribe.devops_agent.entity.PollTimeInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class EmailPollingService {

    private final GraphIntegrationService graphIntegrationService;
    private final EmailService emailService;
    private final PollTimeInfoService pollTimeInfoService;
    private final JenkinsConfig jenkinsConfig;

    /**
     * Get build emails from the email service
     * 
     * @return List of email DTOs
     */
    public List<UserEmailDto> getBuildEmails() {
        String emailId = jenkinsConfig.getEmailId();
        
        try {
//            String dateTimeFilter = "receivedDateTime ge 2025-04-09T20:00:48Z";
            String dateTimeFilter = emailService.getReceivedDateTimeFilter(emailId);
            log.debug("dateTimeFilter: {}", dateTimeFilter);
            
            // Get emails from Graph API
            UserEmailResponseDto emailsOfUser = graphIntegrationService.getEmailsOfUser(
                    emailId, emailId, dateTimeFilter, null, null, "Inbox", 100, 0);

            if (emailsOfUser != null) {
                // Update poll time info
                pollTimeInfoService.setStartTime(emailId, LocalDateTime.now(), PollTimeInfo.Status.SUCCESS);
                pollTimeInfoService.setStartTime(emailId, LocalDateTime.now(), PollTimeInfo.Status.PROCESSING);
            }

            List<List<UserEmailDto>> userMessages = emailsOfUser.getMessages();
            return flattenAndSortEmails(userMessages);
        } catch (Exception e) {
            log.error("Error while getting build emails: {}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    /**
     * Flatten and sort nested email lists
     */
    private List<UserEmailDto> flattenAndSortEmails(List<List<UserEmailDto>> userMessages) {
        if (userMessages == null || userMessages.isEmpty()) {
            return new ArrayList<>();
        }

        return userMessages.stream()
                .flatMap(List::stream) // Flatten the nested list
                .sorted(Comparator.comparing(UserEmailDto::getCreatedTime)) // Sort by createdTime (oldest first)
                .collect(Collectors.toList()); // Collect to List
    }

}
