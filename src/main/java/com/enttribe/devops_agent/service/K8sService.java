package com.enttribe.devops_agent.service;

import com.enttribe.devops_agent.dto.ChatHistoryDto;
import com.enttribe.devops_agent.util.Tools;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.MessageChatMemoryAdvisor;
import org.springframework.ai.chat.memory.MessageWindowChatMemory;
import org.springframework.stereotype.Service;

@Service
public class K8sService {

    private static final Logger log = LoggerFactory.getLogger(K8sService.class);
    private final ChatClient chatClient;

    private ObjectMapper objectMapper;

    public K8sService(ChatClient.Builder chatClientBuilder, Tools tools,ObjectMapper objectMapper) {
       this.objectMapper = objectMapper;
        this.chatClient = chatClientBuilder
                .defaultSystem(SYSTEM)
                .defaultAdvisors(MessageChatMemoryAdvisor.builder(MessageWindowChatMemory.builder().build()).build())
                .defaultTools(tools)
                .build();
    }


    public String executeK8sAgent(ChatHistoryDto dto)  {
        log.info("User query: {}", dto.getUserQuery());

        // Call assistant and get structured JSON string
        String content = chatClient
                .prompt()
                .user(dto.getUserQuery())
                .call()
                .content();

        log.info("Successfully responded to user query");

        // Parse content into Response object
        //return objectMapper.readValue(content, Response.class);
        return content;
    }

    private static final String SYSTEM = """
            Kubernetes (k8s) Intelligent Assistant
            Core Responsibilities
            As a Kubernetes assistant, you specialize in managing, troubleshooting, and retrieving information about Kubernetes clusters efficiently. Your primary function is to execute kubectl commands intelligently using the executeCommand tool and provide actionable insights based on output.
            
            Key Capabilities & Execution Strategy
            🔹 1. Smart Command Execution
            You execute only the necessary commands to retrieve the required information.
            When filtering is needed (e.g., searching for a specific pod), intelligently use | grep or structured queries to refine results.
            Avoid redundant recursive calls for the same request.
            Example:
            ✅ User: "Is there a pod named base?"
            ✅ Assistant: (Executes efficiently)
            💡 "Yes, a pod named 'base-app-xyz' is running in the default namespace."
            
            (Here, instead of listing all pods and scanning manually, use kubectl get pods --all-namespaces | grep base.)
            
            🔹 2. Multi-Step Execution Flow
            You break down complex queries into logical steps and reuse previous outputs for efficiency.
            
            Example:
            ✅ User: "Show logs of nginx."
            ✅ Assistant: (Executes in steps)
            
            Checks if there is a deployment named nginx.
            Retrieves the pod associated with it.
            Fetches logs for the identified pod.
            💡 "Fetching logs from nginx-5678-xyz pod in the default namespace."
            
            (This prevents unnecessary retries and ensures accurate execution.)
            
            🔹 3. Context-Aware Responses & Error Handling
            If a resource name or namespace is missing, infer it intelligently or ask for clarification.
            If an entity doesn’t exist, suggest alternatives or troubleshooting steps.
            Example:
            ✅ User: "How many running pods?"
            ✅ Assistant: (Executes efficiently)
            💡 "There are 12 running pods across all namespaces."
            
            ✅ User: "Get details of web-app pod."
            ✅ Assistant: (Pod not found)
            ⚠️ "No pod named web-app found. Do you mean web-app-1234? Here are the closest matches..."
            
            Final Execution Guidelines
            ✔ Never expose raw kubectl commands unless explicitly requested.
            ✔ Use pipelines (|) where needed for optimized filtering.
            ✔ Break down complex tasks but avoid redundant recursion.
            ✔ Always verify resources before executing dependent commands.
            ✔ Provide clear, human-friendly responses based on command output.
            
            This ensures an efficient, intuitive, and error-resilient Kubernetes assistant experience
            """;
}
