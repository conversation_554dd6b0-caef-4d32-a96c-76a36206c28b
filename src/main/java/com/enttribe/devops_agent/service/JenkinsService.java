package com.enttribe.devops_agent.service;

import com.enttribe.devops_agent.config.JenkinsConfig;
import com.enttribe.devops_agent.jenkins.JenkinsApiClient;
import com.enttribe.devops_agent.jenkins.JenkinsBuildStatusChecker;
import com.enttribe.devops_agent.jenkins.JenkinsBuildTrigger;
import com.enttribe.devops_agent.jenkins.JenkinsQueueChecker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@Slf4j
@RequiredArgsConstructor
public class JenkinsService {

    private final JenkinsConfig jenkinsConfig;

    /**
     * Trigger a Jenkins build
     *
     * @param environment   The environment to deploy to
     * @param jobFor        Whether this is for "frontend" or "backend"
     * @param buildParams   The build parameters
     * @param forProduction Whether this is a production build
     * @param isFirstStage  Whether this is the first stage of build
     * @param isShellApp    Whether this is a shell app (for frontend only)
     * @param isKeycloakApp Whether this is a keycloak app (for backend only)
     * @return A BuildJobResult containing queue URL, build URL, and Blue Ocean URL
     */
    public BuildJobResult triggerJenkinsBuild(String environment, String jobFor, JSONObject buildParams, boolean forProduction,
                                              boolean isFirstStage, boolean isShellApp, boolean isKeycloakApp) throws IOException, InterruptedException {
        log.debug("Triggering Jenkins build for environment: {}, job for: {}, forProduction: {}, isFirstStage: {}, isShellApp: {}, isKeycloakApp: {}, application name: {}",
                environment, jobFor, forProduction, isFirstStage, isShellApp, isKeycloakApp, buildParams.optString("APPLICATION_NAME"));
        // Get Jenkins details for the environment
        JenkinsConfig.JobDetails jenkinsDetails;
        if (forProduction && isFirstStage) {
            jenkinsDetails = jenkinsConfig.getEnvironments().get("dev");
        } else {
            jenkinsDetails = jenkinsConfig.getEnvironments().get(environment.toLowerCase());
        }

        if (jenkinsDetails == null) {
            throw new IllegalArgumentException("No Jenkins configuration found for environment: " + environment);
        }

        // Create Jenkins client
        JenkinsApiClient client = new JenkinsApiClient(
                jenkinsDetails.getJenkinsUrl(),
                jenkinsDetails.getJenkinsUser(),
                jenkinsDetails.getJenkinsToken());

        JenkinsBuildTrigger buildTrigger = new JenkinsBuildTrigger(client);
        JenkinsQueueChecker queueChecker = new JenkinsQueueChecker(client);

        // Determine job name and trigger build
        String jobName;
        if (jobFor.equals("frontend")) {
            if (forProduction && isFirstStage) {
                jobName = isShellApp ? "Gcp_Shell_Ci_Pipeline" : "Gcp_MicroFrontend_Ci_Pipeline";
            } else {
                jobName = isShellApp ? jenkinsDetails.getShellAppJobName() : jenkinsDetails.getFrontendJobName();
            }
            if (buildParams.optString("APPLICATION_NAME").equalsIgnoreCase("gpt")) {
                if (buildParams.optString("ENV").equalsIgnoreCase("dev")) {
                    jobName = "gpt-MicroFrontend_Dev_angular_17";
                } else if (buildParams.optString("ENV").equalsIgnoreCase("demo")) {
                    jobName = "gpt-MicroFrontend_Demo_angular_17";
                }
            }
        } else {
            if (forProduction && isFirstStage) {
                jobName = isKeycloakApp ? "Gcp_keycloak_Backend_Ci_Pipeline" : "Gcp_Backend_Microservice_Ci_Pipeline";
            } else {
                jobName = isKeycloakApp ? jenkinsDetails.getKeycloakJobName() : jenkinsDetails.getBackendJobName();
            }
        }

        log.debug("Job name is: {}", jobName);

        String triggerId = UUID.randomUUID().toString();
        buildParams.put("TRIGGER_ID", triggerId);
        log.info("TRIGGER_ID : {}", triggerId);

        String queueUrl = buildTrigger.triggerBuild(jenkinsDetails.getJenkinsUrl() + "/job/" + jobName, buildParams);
        log.debug("Build Triggered! Queue url is: {}", queueUrl);

        // Instead of waiting on queue item:
        String buildUrl = queueChecker.waitForBuildByTriggerId(jenkinsDetails.getJenkinsUrl(), jobName, triggerId);
        log.debug("Build URL: {}", buildUrl);

        // Get Blue Ocean URL
        String blueOceanUrl = getBlueOceanUrl(buildUrl, jenkinsDetails.getJenkinsUrl());

        return new BuildJobResult(queueUrl, buildUrl, blueOceanUrl);
    }

    public BuildJobResult triggerJenkinsBuild1(String environment, String jobName, JSONObject buildParams) throws IOException, InterruptedException {
        log.debug("Triggering Jenkins build for environment: {}, jobName : {}, application name: {}",
                environment, jobName, buildParams.optString("APPLICATION_NAME"));
        // Get Jenkins details for the environment
        JenkinsConfig.JobDetails jenkinsDetails = jenkinsConfig.getEnvironments().get(environment.toLowerCase());

        if (jenkinsDetails == null) {
            throw new IllegalArgumentException("No Jenkins configuration found for environment: " + environment);
        }

        // Create Jenkins client
        JenkinsApiClient client = new JenkinsApiClient(
                jenkinsDetails.getJenkinsUrl(),
                jenkinsDetails.getJenkinsUser(),
                jenkinsDetails.getJenkinsToken());

        JenkinsBuildTrigger buildTrigger = new JenkinsBuildTrigger(client);
        JenkinsQueueChecker queueChecker = new JenkinsQueueChecker(client);

        log.debug("Job name is: {}", jobName);

        String triggerId = UUID.randomUUID().toString();
        buildParams.put("TRIGGER_ID", triggerId);
        log.info("TRIGGER_ID : {}", triggerId);

        String queueUrl = buildTrigger.triggerBuild(jenkinsDetails.getJenkinsUrl() + "/job/" + jobName, buildParams);
        log.debug("Build Triggered! Queue url is: {}", queueUrl);

        // Instead of waiting on queue item:
        String buildUrl = queueChecker.waitForBuildByTriggerId(jenkinsDetails.getJenkinsUrl(), jobName, triggerId);
        log.debug("Build URL: {}", buildUrl);

        // Get Blue Ocean URL
        String blueOceanUrl = getBlueOceanUrl(buildUrl, jenkinsDetails.getJenkinsUrl());

        return new BuildJobResult(queueUrl, buildUrl, blueOceanUrl);
    }

    /**
     * Check the status of a Jenkins build
     *
     * @param environment  The environment
     * @param blueOceanUrl The Blue Ocean URL to check
     * @return The build status or null if still running
     */
    public String checkBuildStatus(String environment, String blueOceanUrl) throws IOException {
        log.debug("Checking build status for environment: {}, Blue Ocean URL: {}", environment, blueOceanUrl);
        JenkinsConfig.JobDetails jenkinsDetails = jenkinsConfig.getEnvironments().get(environment.toLowerCase());
        if (jenkinsDetails == null) {
            throw new IllegalArgumentException("No Jenkins configuration found for environment: " + environment);
        }

        JenkinsApiClient client = new JenkinsApiClient(
                jenkinsDetails.getJenkinsUrl(),
                jenkinsDetails.getJenkinsUser(),
                jenkinsDetails.getJenkinsToken());

        JenkinsBuildStatusChecker buildStatusChecker = new JenkinsBuildStatusChecker(client);
        return buildStatusChecker.checkBuildStatus(blueOceanUrl);
    }

    /**
     * Check the status of a Jenkins build
     *
     * @param environment  The environment
     * @param blueOceanUrl The Blue Ocean URL to check
     * @return The build status or null if still running
     */
    public String getBuildLogs(String environment, String blueOceanUrl) throws IOException {
        log.debug("fetching build logs for environment: {}, Blue Ocean URL: {}", environment, blueOceanUrl);
        JenkinsConfig.JobDetails jenkinsDetails = jenkinsConfig.getEnvironments().get(environment.toLowerCase());
        if (jenkinsDetails == null) {
            throw new IllegalArgumentException("No Jenkins configuration found for environment: " + environment);
        }

        JenkinsApiClient client = new JenkinsApiClient(
                jenkinsDetails.getJenkinsUrl(),
                jenkinsDetails.getJenkinsUser(),
                jenkinsDetails.getJenkinsToken());

        JenkinsBuildStatusChecker buildStatusChecker = new JenkinsBuildStatusChecker(client);
        return buildStatusChecker.getBuildLogs(blueOceanUrl);
    }

    /**
     * Extract the build number from a build URL
     */
    public String extractBuildNumber(String buildUrl) {
        Pattern pattern = Pattern.compile(".*/(\\d+)/?$"); // Capture last number
        Matcher matcher = pattern.matcher(buildUrl);

        if (matcher.find()) {
            return matcher.group(1);
        } else {
            return "";
        }
    }

    /**
     * Get the Blue Ocean URL for a build
     */
    private String getBlueOceanUrl(String buildUrl, String jenkinsUrl) {
        String[] buildUrlParts = buildUrl.split("/");
        String buildId = buildUrlParts[buildUrlParts.length - 1];
        String jobName = buildUrlParts[buildUrlParts.length - 2];
        return String.format("%s/blue/rest/organizations/jenkins/pipelines/%s/runs/%s/", jenkinsUrl, jobName, buildId);
    }

    /**
     * Result class for Jenkins build operations
     */
    public record BuildJobResult(String queueUrl, String buildUrl, String blueOceanUrl) {
    }

}
