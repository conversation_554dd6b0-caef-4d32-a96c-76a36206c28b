package com.enttribe.devops_agent.service;

import com.enttribe.commons.ai.chat.AiChatModel;
import com.enttribe.devops_agent.config.EmailTemplateConfig;
import com.enttribe.devops_agent.config.JenkinsConfig;
import com.enttribe.devops_agent.constants.BuildConstants;
import com.enttribe.devops_agent.dao.BuildApplicationDao;
import com.enttribe.devops_agent.dto.UserEmailDto;
import com.enttribe.devops_agent.entity.BuildEmails;
import com.enttribe.devops_agent.jenkins.BuildEmailPoller.BackendParameters;
import com.enttribe.devops_agent.jenkins.BuildEmailPoller.FrontendParameters;
import com.enttribe.devops_agent.util.AIUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class BuildEmailProcessingService {

    private final AiChatModel aiChatModel;
    private final MailService mailService;
    private final EmailTemplateConfig emailTemplateConfig;
    private final JenkinsConfig jenkinsConfig;
    private final BuildApplicationDao applicationDao;

    /**
     * Process a build email
     *
     * @param dto           The email DTO
     * @param existingEmail Optional existing BuildEmails entity to update
     * @return A processed BuildEmails entity
     */
    public BuildEmails processEmail(UserEmailDto dto, BuildEmails existingEmail) {
        String conversationId = dto.getConversationId();
        String internetMessageId = dto.getInternetMessageId();
        String messageId = dto.getId();
        String subject = dto.getSubject();
        String body = dto.getBody();
        String from = dto.getFrom();

        log.info("Processing build email with subject: {}", subject);

        BuildEmails buildEmails = existingEmail != null ? existingEmail : new BuildEmails();
        buildEmails.setConversationId(conversationId);
        buildEmails.setMessageId(messageId);
        buildEmails.setContent(body);
        buildEmails.setInternetMessageId(internetMessageId);
        buildEmails.setSubject(subject);
        buildEmails.setEmailFrom(from);
        buildEmails.setCreatedTime(new java.util.Date());

        try {
            if (subject.toLowerCase().contains("backend")) {
                processBackendEmail(buildEmails, subject, body);
            } else if (subject.toLowerCase().contains("frontend")) {
                processFrontendEmail(buildEmails, subject, body);
            } else {
                log.error("Unable to determine job for frontend or backend");
                buildEmails.setError("Unable to determine job for frontend or backend");
                buildEmails.setStatus(BuildConstants.ERROR);
                String issue = "We were unable to determine whether the build is for frontend or backend";
                String draft = AIUtils.getResolvedPrompt(emailTemplateConfig.getFormatIssue(),
                        java.util.Map.of("email", dto.getFrom(), "issue", issue));
                mailService.replyBuildEmail(jenkinsConfig.getEmailId(), dto.getId(), draft);
            }
        } catch (Exception e) {
            log.error("Error processing email with subject: {}", dto.getSubject(), e);
            buildEmails.setStatus(BuildConstants.ERROR);
            buildEmails.setError(e.getMessage());
            sendUnexpectedErrorMail(buildEmails.getMessageId(), buildEmails.getInternetMessageId());
        }

        return buildEmails;
    }

    /**
     * Process a backend build email
     */
    private void processBackendEmail(BuildEmails buildEmails, String subject, String body) {
        BackendParameters backendParameters = aiChatModel.chatCompletion(
                "DEVOPS_SINGULARITY_APP_NAME-Jenkins-Parameter_extractor-v-1",
                java.util.Map.of("subject", subject, "mailContent", body),
                BackendParameters.class);

        buildEmails.setType(backendParameters.TYPE());
        buildEmails.setApplicationName(backendParameters.APPLICATION_NAME());

        String environment = backendParameters.ENV();
        boolean isProductionJob = !(environment.equalsIgnoreCase("dev") || environment.equalsIgnoreCase("demo"));
        buildEmails.setIsProductionJob(isProductionJob);

        buildEmails.setBuildParameters(AIUtils.convertToJSON(backendParameters, true));

        if (backendParameters.TYPE().equalsIgnoreCase(BuildConstants.REQUESTEE)) {
            buildEmails.setStatus(BuildConstants.PENDING);
        }
    }

    /**
     * Process a frontend build email
     */
    private void processFrontendEmail(BuildEmails buildEmails, String subject, String body) {
        FrontendParameters frontendParameters = aiChatModel.chatCompletion(
                "DEVOPS_SINGULARITY_APP_NAME-Jenkins-Parameter_extractor-v-1",
                java.util.Map.of("subject", subject, "mailContent", body),
                FrontendParameters.class);

        buildEmails.setType(frontendParameters.TYPE());
        buildEmails.setApplicationName(frontendParameters.APPLICATION_NAME());

        String environment = frontendParameters.ENV();
        boolean isProductionJob = !(environment.equalsIgnoreCase("dev") || environment.equalsIgnoreCase("demo"));
        buildEmails.setIsProductionJob(isProductionJob);

        buildEmails.setBuildParameters(AIUtils.convertToJSON(frontendParameters, true));

        if (frontendParameters.TYPE().equalsIgnoreCase(BuildConstants.REQUESTEE)) {
            buildEmails.setStatus(BuildConstants.PENDING);
        }
    }

    /**
     * Check if a user is authorized to approve builds
     */
    public boolean isAuthorizedApprover(String email, String applicationName, String subject) {
        String emailId = email.trim().toLowerCase();
        String type = subject.toLowerCase().contains("frontend") ? "frontend" : "backend";
        // Step 1: Check in YAML approvers
        List<String> approversFromYaml = emailTemplateConfig.getApprovers();
        if (approversFromYaml != null && approversFromYaml.stream()
                .map(String::toLowerCase)
                .anyMatch(approver -> approver.equals(emailId))) {
            log.debug("Approver {} is super approver and authorized to approve jenkins pipeline.", emailId);
            return true;
        }

        // Step 2: Check in DB-configured approvers (comma-separated)
        String approversFromDb = applicationDao.getListOfApproveEmails(applicationName,type);
        if (approversFromDb != null) {
            return Arrays.stream(approversFromDb.split(","))
                    .map(String::trim)
                    .map(String::toLowerCase)
                    .anyMatch(approver -> approver.equals(emailId));
        }
        log.debug("Approver {} is not authorized to approve jenkins pipeline.", emailId);
        return false;
    }

    /**
     * Send an unexpected error email
     */
    public void sendUnexpectedErrorMail(String messageId, String internetMessageId) {
        try {
            String errorTemplate = emailTemplateConfig.getUnexpectedErrorTemplate();
            if (errorTemplate == null) {
                errorTemplate = """
                        Dear team,
                        
                        Your request could not be processed due to an unexpected error.
                        
                        Please contact Devops team or initiate another request.
                        
                        Request id : %s
                        
                        Best Regards,
                        AI Devops(visionwaves)
                        Where automation comes first!!!
                        """;
            }

            mailService.replyBuildEmail(
                    jenkinsConfig.getEmailId(),
                    messageId,
                    String.format(errorTemplate, internetMessageId));
        } catch (Exception ex) {
            log.error("Error while sending email: {}", ex.getMessage(), ex);
        }
    }

    /**
     * Extract build parameters from JSON
     */
    public JSONObject extractBuildParameters(String buildParametersJson) {
        return new JSONObject(buildParametersJson);
    }

    /**
     * Check if the environment is supported
     */
    public boolean isSupportedEnvironment(String environment) {
        List<String> supportedEnvironments = jenkinsConfig.getSupportedEnvironments();
        return supportedEnvironments != null && supportedEnvironments.contains(environment.toLowerCase());
    }

}
