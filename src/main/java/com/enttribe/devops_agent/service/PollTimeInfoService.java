package com.enttribe.devops_agent.service;

import com.enttribe.devops_agent.dao.PollTimeInfoDao;
import com.enttribe.devops_agent.entity.PollTimeInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Optional;

@Service
@Slf4j
@RequiredArgsConstructor
public class PollTimeInfoService {

    private final PollTimeInfoDao pollTimeInfoDao;

    public LocalDateTime fetchTimeToPoll(String email) {
        log.debug("Inside @method fetchTimeToPoll. @param :email -> {}", email);
        Optional<PollTimeInfo> endTimeInfo = pollTimeInfoDao.findFirstByEmailAndStatusOrderByEndTimeDesc(email, PollTimeInfo.Status.SUCCESS);

        if (endTimeInfo.isEmpty()) {
            PollTimeInfo pollTimeInfo = new PollTimeInfo();
            pollTimeInfo.setStartTime(LocalDateTime.now());
            pollTimeInfo.setEndTime(LocalDateTime.now());
            pollTimeInfo.setStatus(PollTimeInfo.Status.PROCESSING);
            pollTimeInfo.setEmail(email);
            PollTimeInfo saved = pollTimeInfoDao.save(pollTimeInfo);
            return saved.getStartTime().withHour(0).withMinute(0);
        }

        PollTimeInfo.Status status = endTimeInfo.get().getStatus();
        if (status != null && status.equals(PollTimeInfo.Status.SUCCESS)) {
            Optional<PollTimeInfo> startTimeInfo = pollTimeInfoDao.findFirstByEmailOrderByStartTimeDesc(email);
            return startTimeInfo.isEmpty() ? null : startTimeInfo.get().getStartTime();
        }
        return null;
    }

    public void setStartTime(String email, LocalDateTime localDateTime, PollTimeInfo.Status status) {
        log.debug("Inside @method setStartTime. @param :email -> {} localDateTime -> {} status -> {}",
                email, localDateTime, status);
        PollTimeInfo pollTimeInfo = new PollTimeInfo();
        pollTimeInfo.setStartTime(localDateTime);
        pollTimeInfo.setStatus(status);
        pollTimeInfo.setEmail(email);
        pollTimeInfoDao.save(pollTimeInfo);
    }
}
