package com.enttribe.devops_agent.service;

import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;

@Service
public class TokenService {

    private static final String TOKEN_URL = "https://login.microsoftonline.com/e89a6dbc-26a3-4272-bea3-bbefc5f488d0/oauth2/v2.0/token";
    private static final String CLIENT_ID = "7690d180-b976-4ebe-9d17-3ed63aa6469b";
    private static final String CLIENT_SECRET = "****************************************";
    private static final String SCOPE = "https://graph.microsoft.com/.default";
    private static final String GRANT_TYPE = "client_credentials";

    private final RestTemplate restTemplate = new RestTemplate();

    public String getAccessToken() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        String requestBody = buildFormData(Map.of(
                "client_id", CLIENT_ID,
                "client_secret", CLIENT_SECRET,
                "scope", SCOPE,
                "grant_type", GRANT_TYPE
        ));

        HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, headers);

        ResponseEntity<Map> response = restTemplate.postForEntity(TOKEN_URL, requestEntity, Map.class);

        if (response.getStatusCode().is2xxSuccessful()) {
            Map<String, Object> body = response.getBody();
            if (body != null && body.containsKey("access_token")) {
                return (String) body.get("access_token");
            }
        }

        throw new RuntimeException("Failed to retrieve access token: " + response.getStatusCode());
    }

    private String buildFormData(Map<String, String> data) {
        StringBuilder result = new StringBuilder();
        for (Map.Entry<String, String> entry : data.entrySet()) {
            if (result.length() > 0) {
                result.append("&");
            }
            result.append(URLEncoder.encode(entry.getKey(), StandardCharsets.UTF_8));
            result.append("=");
            result.append(URLEncoder.encode(entry.getValue(), StandardCharsets.UTF_8));
        }
        return result.toString();
    }
}
