package com.enttribe.devops_agent.service;

import com.enttribe.devops_agent.config.EmailTemplateConfig;
import com.enttribe.devops_agent.config.JenkinsConfig;
import com.enttribe.devops_agent.constants.BuildConstants;
import com.enttribe.devops_agent.dao.BuildEmailDao;
import com.enttribe.devops_agent.dto.UserEmailDto;
import com.enttribe.devops_agent.entity.BuildEmails;
import com.enttribe.devops_agent.service.JenkinsService.BuildJobResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class BuildApprovalService {

    private final BuildEmailDao buildEmailDao;
    private final MailService mailService;
    private final JenkinsService jenkinsService;
    private final BuildEmailProcessingService emailProcessingService;
    private final BuildStatusService buildStatusService;
    private final EmailTemplateConfig emailTemplateConfig;
    private final JenkinsConfig jenkinsConfig;
    private final EnvironmentDeploymentConfigService environmentDeploymentConfigService;
    private final EnvironmentRestrictionService environmentRestrictionService;

    /**
     * Process an approval email
     *
     * @param dto         The email DTO
     * @param buildEmails The build email entity
     */
    public void processApprovalEmail(UserEmailDto dto, BuildEmails buildEmails) {
        log.debug("Processing approver's email. Approver: {}", dto.getFrom());

        // Check for environment-specific restrictions
        EnvironmentRestrictionService.RestrictionResult restrictionResult =
            environmentRestrictionService.checkBuildRestriction(dto.getFrom(), buildEmails.getApplicationName(), dto.getSubject());

        if (restrictionResult.isRestricted()) {
            log.info("Build is restricted for approver: {} on subject: {}", dto.getFrom(), dto.getSubject());
            mailService.replyBuildEmail(jenkinsConfig.getEmailId(), dto.getId(), restrictionResult.message());
            return;
        }

        // Check if user is authorized to approve
        if (!emailProcessingService.isAuthorizedApprover(dto.getFrom(), buildEmails.getApplicationName(), dto.getSubject())) {
            log.error("{} is not allowed to approve jenkins pipeline.", dto.getFrom());
            mailService.replyBuildEmail(jenkinsConfig.getEmailId(), dto.getId(), emailTemplateConfig.getUnauthorizedTemplate());
            buildEmails.setStatus(BuildConstants.ERROR);
            buildEmails.setError("Not allowed to approve jenkins pipeline");
            return;
        }

        // Get pending approval email
        List<BuildEmails> forApprovalEmails = buildEmailDao.getForApprovalEmails(dto.getConversationId());
        if (forApprovalEmails.isEmpty()) {
            log.error("No emails pending for approval found for conversation: {}", dto.getConversationId());
            return;
        }

        BuildEmails emailPendingForApproval = forApprovalEmails.getFirst();
        String buildParameters = emailPendingForApproval.getBuildParameters();

        // Parse build parameters
        JSONObject buildParams = emailProcessingService.extractBuildParameters(buildParameters);
        String environment = buildParams.optString("ENV", null);

        // Validate environment
        if (environment == null || !emailProcessingService.isSupportedEnvironment(environment)) {
            log.error("Unable to determine environment for deployment");
            emailPendingForApproval.setStatus(BuildConstants.ERROR);
            emailPendingForApproval.setError("Unsupported environment!!!");
            buildEmailDao.save(emailPendingForApproval);
            return;
        }

        // Check deployment limits
        if (buildStatusService.isDeploymentLimitReached(emailPendingForApproval.getApplicationName(), environment, buildEmails.getSubject())) {
            mailService.replyBuildEmail(jenkinsConfig.getEmailId(), dto.getId(), emailTemplateConfig.getLimitedDeploymentTemplate());
            return;
        }

        // Get approval status from the email
        JSONObject jsonObject = emailProcessingService.extractBuildParameters(buildEmails.getBuildParameters());
        boolean isApproved = jsonObject.optBoolean("APPROVED", false);
        if (!isApproved) {
            emailPendingForApproval.setError("Email is not approved by: " + dto.getFrom());
            buildEmailDao.save(emailPendingForApproval);
            return;
        }

        // Process the approved build
        processApprovedBuild(dto, emailPendingForApproval, buildParams, environment);
    }

    /**
     * Process an approved build
     */
    private void processApprovedBuild(UserEmailDto dto, BuildEmails emailPendingForApproval,
                                      JSONObject buildParams, String environment) {
        emailPendingForApproval.setReplyTo(dto.getId());

        boolean forProduction = emailPendingForApproval.getIsProductionJob();

        // For non-production builds, check deployment window
        // For production builds, always trigger first job (dev environment) immediately
        if (!forProduction && !environmentDeploymentConfigService.isDeploymentAllowed(environment)) {
            log.info("Deployment not allowed at current time for environment: {}. Queueing build for later processing.", environment);
            emailPendingForApproval.setStatus(BuildConstants.QUEUED);
            emailPendingForApproval.setApprovedBy(dto.getFrom());
            buildEmailDao.save(emailPendingForApproval);
            return;
        }

        try {
            // Determine job type (frontend/backend) and shell app status
            String jobFor = emailPendingForApproval.getSubject().toLowerCase().contains("frontend") ? "frontend" : "backend";
            boolean isShellApp = false;
            boolean isKeycloakApp = false;
            if (jobFor.equals("frontend")) {
                isShellApp = buildParams.optBoolean("IS_SHELL_APP", false);
            } else {
                isKeycloakApp = buildParams.optBoolean("IS_KEYCLOAK_APP", false);
            }

            // Trigger Jenkins build
            BuildJobResult buildResult = jenkinsService.triggerJenkinsBuild(
                    environment, jobFor, buildParams, forProduction, true, isShellApp, isKeycloakApp);

            // Update build email with job details
            emailPendingForApproval.setQueueUrl(buildResult.queueUrl());
            emailPendingForApproval.setBuildUrl(buildResult.buildUrl());
            emailPendingForApproval.setStatus(BuildConstants.WAITING);
            emailPendingForApproval.setBlueOceanUrl(buildResult.blueOceanUrl());
            emailPendingForApproval.setApprovedBy(dto.getFrom());

            buildEmailDao.save(emailPendingForApproval);
        } catch (Exception e) {
            log.error("Error in Jenkins pipeline: {}", e.getMessage(), e);
            emailPendingForApproval.setStatus(BuildConstants.ERROR);
            emailPendingForApproval.setError(e.getMessage());
            emailProcessingService.sendUnexpectedErrorMail(emailPendingForApproval.getReplyTo(), emailPendingForApproval.getInternetMessageId());
            buildEmailDao.save(emailPendingForApproval);
        }
    }
}
