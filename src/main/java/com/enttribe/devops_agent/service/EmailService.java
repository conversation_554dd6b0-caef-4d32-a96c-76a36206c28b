package com.enttribe.devops_agent.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

@Slf4j
@Service
@RequiredArgsConstructor
public class EmailService {

    private final PollTimeInfoService pollTimeInfoService;

    public String getReceivedDateTimeFilter(String email) {
        LocalDateTime timeToPoll = pollTimeInfoService.fetchTimeToPoll(email);
        if (timeToPoll == null) {
            timeToPoll = LocalDateTime.now().withHour(0).withMinute(0);
        }
        ZonedDateTime zonedDateTime = timeToPoll.atZone(ZoneId.systemDefault());
        ZonedDateTime utcDateTime = zonedDateTime.withZoneSameInstant(ZoneId.of("UTC"));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'");
        String dateString = utcDateTime.format(formatter);

        return "receivedDateTime ge " + dateString;
    }
}
