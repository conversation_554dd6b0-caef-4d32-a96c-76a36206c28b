package com.enttribe.devops_agent.service;


import com.enttribe.devops_agent.constants.EmailConstants;
import com.enttribe.devops_agent.dto.UserEmailDto;
import com.enttribe.devops_agent.dto.UserEmailResponseDto;
import com.enttribe.devops_agent.util.AIUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.google.common.base.Throwables.getStackTraceAsString;

@Service
@Slf4j
@RequiredArgsConstructor
public class GraphIntegrationService {

    private final TokenService tokenService;

    public UserEmailResponseDto getEmailsOfUser(String userId, String email, String receivedDateTime, String categories, Boolean isRead, String mailFolder, Integer limit, Integer offset) throws IOException, InterruptedException {
        log.debug("Inside @method getEmailsOfUser. @param: email -> {}, receivedDateTime -> {}, categories -> {}, isRead -> {}, limit -> {}, offset -> {}",
                email, receivedDateTime, categories, isRead, limit, offset);
        String url = buildUrlWithFilterAndPagination(email, receivedDateTime, categories, isRead, mailFolder, limit, offset);
        log.debug("URL to get emails : {}", url);
        // Create an HttpClient instance
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // Create a GET request
            HttpGet request = new HttpGet(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenService.getAccessToken());
            request.setHeader(EmailConstants.PREFER, EmailConstants.OUTLOOK_BODY_CONTENT_TYPE_TEXT);
            // Execute the request
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                HttpEntity entity = response.getEntity();
                if (entity != null && response.getStatusLine().getStatusCode() == 200) {
                    String result = EntityUtils.toString(entity);
                    List<UserEmailDto> response1 = convertJsonToUserEmailDto(result);
                    List<List<UserEmailDto>> messages = groupByEmailConversation(response1);
                    UserEmailResponseDto responseDto = new UserEmailResponseDto();
                    responseDto.setMailBoxUserEmail(email);
                    responseDto.setMailBoxUserId(userId);
                    responseDto.setMessages(messages);
                    return responseDto;
                } else if (entity != null) {
                    String errorMessage = EntityUtils.toString(entity);
                    log.error("errorMessage inside @method getEmailsOfUser : {}", errorMessage);
                }
            }
        }

        return null;
    }

    String buildUrlWithFilterAndPagination(String email, String receivedDateTime, String categories, Boolean isRead, String mailFolder, Integer limit, Integer offset) {
        StringBuilder url = new StringBuilder("https://graph.microsoft.com/v1.0/users/")
                .append(email)
                .append("/mailFolders/")
                .append(mailFolder)
                .append("/messages?")
                .append("$filter=isDraft eq false");


        if (isRead != null) {
            url.append(EmailConstants.AND).append("isRead eq ").append(isRead);
        }

        if (receivedDateTime != null) {
            url.append(EmailConstants.AND).append(receivedDateTime);
        }

        if (categories != null) {
            url.append(EmailConstants.AND);
            String[] categoryArray = categories.split(",");

            for (int i = 0; i < categoryArray.length; i++) {
                if (i == 0) {
                    url.append("(");
                    url.append("categories/any(c:c eq '").append(categoryArray[i].trim()).append("')");
                } else {
                    url.append(" or categories/any(c:c eq '").append(categoryArray[i].trim()).append("')");
                }
            }
            url.append(")");
        }

        if (limit != null && offset != null) {
            url.append("&$skip=").append(offset).append("&$top=").append(limit);
        }

        return url.toString().replace(" ", "%20");
    }


    List<UserEmailDto> convertJsonToUserEmailDto(String jsonString) {
        JSONObject jsonObject = new JSONObject(jsonString);

        JSONArray messages = jsonObject.getJSONArray(EmailConstants.VALUE);

        List<UserEmailDto> resultList = new ArrayList<>();

        for (int i = 0; i < messages.length(); i++) {
            JSONObject message = messages.getJSONObject(i);

            String id = message.getString("id");
            Boolean hasAttachments = message.getBoolean("hasAttachments");
            String internetMessageId = message.getString("internetMessageId");
            String subject;
            try {
                subject = message.getString(EmailConstants.SUBJECT);
            } catch (JSONException e) {
                subject = "";
            }
            String conversationId = message.getString("conversationId");
            String bodyPreview = message.getString("bodyPreview");
            String body = message.getJSONObject("body").getString(EmailConstants.CONTENT);
            int indexOfMailEnd = body.indexOf("From: ");
            if (indexOfMailEnd != -1) {
                body = body.substring(0, indexOfMailEnd);
            }

            String from = "";
            if (message.has("from")) {
                from = message.getJSONObject("from").getJSONObject(EmailConstants.EMAIL_ADDRESS).getString(EmailConstants.ADDRESS);
            }

            // Assuming 'createdDateTime' is in ISO 8601 format
            Date createdDateTime = java.util.Date.from(java.time.Instant.parse(message.getString("receivedDateTime")));

            UserEmailDto userEmailDto = new UserEmailDto();
            userEmailDto.setId(id);
            userEmailDto.setFrom(from);
            userEmailDto.setSubject(subject);
            userEmailDto.setBodyPreview(getMeetingPreview(bodyPreview));
            userEmailDto.setBody(body);
            userEmailDto.setConversationId(conversationId);
            userEmailDto.setCreatedTime(createdDateTime);
            userEmailDto.setHasAttachments(hasAttachments);
            userEmailDto.setInternetMessageId(internetMessageId);

            resultList.add(userEmailDto);
        }
        return resultList;
    }

    private String getMeetingPreview(String bodyPreview) {
        if (bodyPreview == null || bodyPreview.startsWith("_________________________________________")) {
            return "";
        }
        return bodyPreview;
    }


    List<List<UserEmailDto>> groupByEmailConversation(List<UserEmailDto> userEmailDtos) {
        // Create a HashMap to group UserEmailDto objects by conversationId
        Map<String, List<UserEmailDto>> conversationMap = new HashMap<>();

        // Iterate through each UserEmailDto
        for (UserEmailDto email : userEmailDtos) {
            // Get the conversationId
            String conversationId = email.getConversationId();

            // Check if the conversationId already exists in the map
            if (conversationMap.containsKey(conversationId)) {
                // If yes, add the email to the existing list
                conversationMap.get(conversationId).add(email);
            } else {
                // If no, create a new list with this email and put it in the map
                List<UserEmailDto> newList = new ArrayList<>();
                newList.add(email);
                conversationMap.put(conversationId, newList);
            }
        }

        // Convert the values of the map (lists of emails) to a List<List<UserEmailDto>>
        List<List<UserEmailDto>> resultList = new ArrayList<>(conversationMap.values());

        // Sort the lists based on createdTime if they contain more than one item
        for (List<UserEmailDto> emailList : resultList) {
            if (emailList.size() > 1) {
                emailList.sort(Comparator.comparing(UserEmailDto::getCreatedTime));
            }
        }

        return resultList;
    }


}