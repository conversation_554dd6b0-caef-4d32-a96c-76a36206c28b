package com.enttribe.devops_agent.service;


import com.enttribe.devops_agent.constants.EmailConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.stereotype.Service;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.util.Base64;


@Service
@Slf4j
@RequiredArgsConstructor
public class MailService {

    private final TokenService tokenService;

    public String replyBuildEmail(String userid, String messageId, String content) {
        log.debug("Inside @method reply");

        String url = String.format("https://graph.microsoft.com/v1.0/users/%s/messages/%s/replyAll", userid, messageId);


        // Create an HttpClient instance
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            JSONObject replyObject = createReplyObject(content);
            String valueAsString = replyObject.toString();
            // Create a PATCH request
            HttpPost request = new HttpPost(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenService.getAccessToken());
            request.setHeader("Content-Type", "application/json");

            StringEntity entity = new StringEntity(valueAsString);
            request.setEntity(entity);

            // Execute the request
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                if (response.getStatusLine().getStatusCode() == 202) {
                    return EmailConstants.SUCCESS;
                }
                String errorMessage = EntityUtils.toString(response.getEntity());
                log.error("Error inside @method reply. Exception message : {}", errorMessage);
                return "failed";
            }
        } catch (Exception e) {
            log.error("Error inside @method reply. Exception message : {}", e.getMessage(), e);
            return "failed";
        }
    }

    private JSONObject createReplyObject(String content) {

        JSONObject body = new JSONObject();
        body.put("contentType", "text");
        body.put("content", content);

        JSONObject message = new JSONObject();
        message.put("body", body);

        JSONObject mainObject = new JSONObject();
        mainObject.put(EmailConstants.MESSAGE, message);

        return mainObject;
    }


    public String replyWithAttachment(String userId, String messageId, String content, BufferedInputStream attachment, String attachmentFileName) throws Exception {
//        return null;
        log.debug("Inside @method replyWithAttachment");

        String url = String.format("https://graph.microsoft.com/v1.0/users/%s/messages/%s/replyAll", userId, messageId);
        log.debug("URL to reply with attachment: {}", url);

        JSONObject replyObject = createReplyObjectWithAttachment(content, attachment, attachmentFileName);

        String valueAsString = replyObject.toString();

        // Create an HttpClient instance
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // Create a PATCH request
            HttpPost request = new HttpPost(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenService.getAccessToken());
            request.setHeader("Content-Type", "application/json");

            StringEntity entity = new StringEntity(valueAsString);
            request.setEntity(entity);

            // Execute the request
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                if (response.getStatusLine().getStatusCode() == 202) {
                    return EmailConstants.SUCCESS;
                }
                String errorMessage = EntityUtils.toString(response.getEntity());
                log.error("Error inside @method replyWithAttachment. Exception message: {}", errorMessage);
                return "failed";
            }
        }
    }

    private JSONObject createReplyObjectWithAttachment(String content, BufferedInputStream attachment, String attachmentFileName) throws IOException {
        JSONObject messageBody = new JSONObject();
        messageBody.put("contentType", "Text");
        messageBody.put("content", content);

        JSONObject message = new JSONObject();
        message.put(EmailConstants.MESSAGE, new JSONObject().put("body", messageBody));

        // Encode the attachment file content in base64
        byte[] attachmentBytes = attachment.readAllBytes();
        String encodedAttachment = Base64.getEncoder().encodeToString(attachmentBytes);

        JSONArray attachments = new JSONArray();
        JSONObject attachmentObject = new JSONObject();
        attachmentObject.put("@odata.type", "#microsoft.graph.fileAttachment");
        attachmentObject.put("name", attachmentFileName);
        attachmentObject.put("contentBytes", encodedAttachment);
        attachments.put(attachmentObject);

        message.getJSONObject(EmailConstants.MESSAGE).put("attachments", attachments);

        return message;
    }

    public String sendMail(String emailOfUser, String subject, String content, String toEmail, String ccEmail, String bccEmail, Boolean sendDraft) {
        log.debug("Inside @method createDraft");

        String url = String.format("https://graph.microsoft.com/v1.0/users/%s/messages", emailOfUser);
        log.debug("URL to createDraft : {}", url);

        // Create JSON payload
        JSONObject jsonBody = new JSONObject();
        jsonBody.put(EmailConstants.SUBJECT, subject);

        JSONObject bodyContent = new JSONObject();
        bodyContent.put(EmailConstants.CONTENT_TYPE, "HTML");
        bodyContent.put(EmailConstants.CONTENT, content);
        jsonBody.put("body", bodyContent);

        // Add toRecipients
        JSONArray toRecipients = new JSONArray();
        for (String email : toEmail.split(",")) {
            JSONObject emailObject = new JSONObject();
            emailObject.put(EmailConstants.ADDRESS, email.trim());
            JSONObject recipient = new JSONObject();
            recipient.put(EmailConstants.EMAIL_ADDRESS, emailObject);
            toRecipients.put(recipient);
        }
        jsonBody.put("toRecipients", toRecipients);

        // Add ccRecipients
        if (ccEmail != null && !ccEmail.isEmpty()) {
            JSONArray ccRecipients = new JSONArray();
            for (String email : ccEmail.split(",")) {
                JSONObject emailObject = new JSONObject();
                emailObject.put(EmailConstants.ADDRESS, email.trim());
                JSONObject recipient = new JSONObject();
                recipient.put(EmailConstants.EMAIL_ADDRESS, emailObject);
                ccRecipients.put(recipient);
            }
            jsonBody.put("ccRecipients", ccRecipients);
        }

        // Add bccRecipients
        if (bccEmail != null && !bccEmail.isEmpty()) {
            JSONArray bccRecipients = new JSONArray();
            for (String email : bccEmail.split(",")) {
                JSONObject emailObject = new JSONObject();
                emailObject.put(EmailConstants.ADDRESS, email.trim());
                JSONObject recipient = new JSONObject();
                recipient.put(EmailConstants.EMAIL_ADDRESS, emailObject);
                bccRecipients.put(recipient);
            }
            jsonBody.put("bccRecipients", bccRecipients);
        }
//        jsonBody.put("saveToSentItems", true);

        // Create an HttpClient instance
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // Create a POST request
            HttpPost request = new HttpPost(url);
            request.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenService.getAccessToken());
            request.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

            StringEntity entity = new StringEntity(jsonBody.toString());
            request.setEntity(entity);

            // Execute the request
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                if (response.getStatusLine().getStatusCode() == 201) {
                    String responseBody = EntityUtils.toString(response.getEntity());
                    JSONObject responseJson = new JSONObject(responseBody);
                    String draftMessageId = responseJson.getString("id");
                    if (sendDraft) {
                        return sendDraftEmail(httpClient, emailOfUser, draftMessageId);
                    }
                    return EmailConstants.SUCCESS;
                } else {
                    String responseBody = EntityUtils.toString(response.getEntity());
                    log.error("Error creating draft: {}", responseBody);
                    return EmailConstants.FAILED;
                }
            }
        } catch (Exception e) {
            log.error("Error creating draft: {}", e.getMessage(), e);
            return EmailConstants.FAILED;
        }
    }

    private String sendDraftEmail(CloseableHttpClient httpClient, String userId, String draftMessageId) {
        String sendUrl = String.format("https://graph.microsoft.com/v1.0/users/%s/messages/%s/send", userId, draftMessageId);
        log.debug("URL to sendDraftEmail : {}", sendUrl);

        // Create a POST request to send the draft
        HttpPost sendRequest = new HttpPost(sendUrl);
        sendRequest.setHeader(EmailConstants.AUTHORIZATION, EmailConstants.BEARER + tokenService.getAccessToken());
        sendRequest.setHeader(EmailConstants.CONTENT_TYPE, EmailConstants.APPLICATION_JSON);

        // Execute the request to send the email
        try (CloseableHttpResponse sendResponse = httpClient.execute(sendRequest)) {
            int sendStatusCode = sendResponse.getStatusLine().getStatusCode();
            if (sendStatusCode == 202) {
                return EmailConstants.SUCCESS;
            }
            String sendErrorMessage = EntityUtils.toString(sendResponse.getEntity());
            log.error("Error sending draft email. Status code -> {}, Error message -> {}", sendStatusCode, sendErrorMessage);
            return EmailConstants.FAILED;
        } catch (Exception e) {
            log.error("Error inside @method sendDraftEmail. Exception message -> {}", e.getMessage());
            return EmailConstants.FAILED;
        }
    }

}
