package com.enttribe.devops_agent.service;

import com.enttribe.devops_agent.dao.EnvironmentRestrictionDao;
import com.enttribe.devops_agent.entity.EnvironmentRestriction;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * Service for managing environment-specific build restrictions.
 * Handles checking if builds are restricted for certain environments and approvers.
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class EnvironmentRestrictionService {

    private final EnvironmentRestrictionDao environmentRestrictionDao;

    /**
     * Check if a build is restricted based on environment, subject, and approver
     *
     * @param email           The approver's email
     * @param applicationName The application name
     * @param subject         The email subject
     * @return RestrictionResult containing restriction status and message
     */
    public RestrictionResult checkBuildRestriction(String email, String applicationName, String subject) {
        log.debug("Checking build restriction for email: {}, application: {}, subject: {}",
                email, applicationName, subject);

        // Get all active restrictions that match the subject pattern
        List<EnvironmentRestriction> restrictions = environmentRestrictionDao.findAllActiveRestrictions();

        for (EnvironmentRestriction restriction : restrictions) {
            // Check if the subject matches the restriction pattern
            if (isSubjectMatching(subject, restriction)) {
                log.info("Found matching restriction for pattern: {} in subject: {}",
                        restriction.getRestrictionPattern(), subject);

                // Check if the approver is in the allowed list
                if (!isApproverAllowed(email, restriction.getAllowedApprovers())) {
                    log.info("Approver {} is not in allowed list for restricted environment: {}",
                            email, restriction.getEnvironment());

                    return new RestrictionResult(
                            true,
                            restriction.getRestrictionMessage() != null ?
                                    restriction.getRestrictionMessage() : getDefaultRestrictionMessage(restriction.getEnvironment())
                    );
                }
            }
        }

        log.debug("No restrictions found for the build request");
        return new RestrictionResult(false, null);
    }

    /**
     * Check if the subject matches the restriction pattern
     */
    private boolean isSubjectMatching(String subject, EnvironmentRestriction restriction) {
        if (restriction.getRestrictionPattern() == null || restriction.getRestrictionPattern().trim().isEmpty()) {
            return false;
        }

        String pattern = restriction.getRestrictionPattern().toLowerCase().replace(" ", "");
        String[] environmentDetails = pattern.split(",");

        if (environmentDetails.length > 1) {
            return Arrays.stream(environmentDetails)
                    .allMatch(env -> subject.toLowerCase().contains(env.trim()));
        } else {
            return subject.toLowerCase().contains(pattern) && !subject.toLowerCase().contains("humain");
        }
    }

    private static boolean isSubjectMatching(String subject, String pattern) {
        String[] environmentDetails = pattern.split(",");

        if (environmentDetails.length > 1) {
            return Arrays.stream(environmentDetails)
                    .allMatch(env -> subject.toLowerCase().contains(env.trim()));
        } else {
            return subject.toLowerCase().contains(pattern) && !subject.toLowerCase().contains("humain");
        }
    }

    public static void main(String[] args) {
        String subject = "Request to upgrade frontend deployment on humainos dev Environment";
        String pattern = "humainos,dev";

        EnvironmentRestrictionService environmentRestrictionService = new EnvironmentRestrictionService(null);

        String email = "<EMAIL>";
        String allowedApprovers = "<EMAIL>,<EMAIL>";

        System.out.println(environmentRestrictionService.isApproverAllowed(email, allowedApprovers));


    }

    /**
     * Check if the approver is in the allowed approvers list
     */
    private boolean isApproverAllowed(String email, String allowedApprovers) {
        if (allowedApprovers == null || allowedApprovers.trim().isEmpty()) {
            return false; // If no approvers specified, no one is allowed
        }

        String emailLower = email.toLowerCase().trim();
        return Arrays.stream(allowedApprovers.split(","))
                .map(String::trim)
                .map(String::toLowerCase)
                .anyMatch(approver -> approver.equals(emailLower));
    }

    /**
     * Get default restriction message
     */
    private String getDefaultRestrictionMessage(String environment) {
        return String.format("""
                Dear Team,
                
                Automated deployment requests for %s environment are currently restricted.
                
                Please contact the DevOps team for manual deployment:
                
                📩 <EMAIL>
                
                Best regards,
                AI DevOps (Visionwaves)
                Where automation comes first!!!
                """, environment);
    }

    /**
     * Result class for restriction checks
     */
    public record RestrictionResult(boolean isRestricted, String message) {

    }
}
