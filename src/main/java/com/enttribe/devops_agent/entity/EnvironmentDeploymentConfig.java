package com.enttribe.devops_agent.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalTime;
import java.util.Date;

/**
 * Entity to store deployment time window configurations for different environments.
 * This allows controlling when builds can be deployed to specific environments.
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "ENVIRONMENT_DEPLOYMENT_CONFIG")
public class EnvironmentDeploymentConfig {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer id;

    @Column(name = "ENVIRONMENT", nullable = false, unique = true)
    private String environment;

    @Column(name = "DEPLOYMENT_START_TIME")
    private LocalTime deploymentStartTime;

    @Column(name = "DEPLOYMENT_END_TIME")
    private LocalTime deploymentEndTime;

    @Column(name = "SECONDARY_START_TIME")
    private LocalTime secondaryStartTime;

    @Column(name = "SECONDARY_END_TIME")
    private LocalTime secondaryEndTime;

    @Column(name = "IS_ACTIVE")
    private Boolean isActive = true;

    @Column(name = "CREATED_DATE")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdDate;

    @Column(name = "UPDATED_DATE")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedDate;

    @PrePersist
    protected void onCreate() {
        createdDate = new Date();
        updatedDate = new Date();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedDate = new Date();
    }
}
