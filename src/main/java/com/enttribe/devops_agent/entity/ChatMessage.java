package com.enttribe.devops_agent.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Entity
@Setter
@Getter
@Table(name = "CHAT_MESSAGE")
public class ChatMessage {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;

    @Column(name = "USER_QUERY", columnDefinition = "TEXT")
    private String userQuery;

    @Column(name = "ASSISTANT_RESPONSE", columnDefinition = "TEXT")
    private String assistantResponse;

    @Column(name = "START_TIME")
    private LocalDateTime startTime;

    @Column(name = "CONVERSATION_ID", length = 255)
    private String conversationId;

}
