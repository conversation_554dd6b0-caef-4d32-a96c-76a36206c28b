package com.enttribe.devops_agent.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "PRODUCTION_BUILD")
public class ProductionBuild {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer id;

    @ManyToOne
    @JoinColumn(name = "BUILD_EMAIL_ID")
    private BuildEmails buildEmail;

    @Column(name = "INTERNET_MESSAGE_ID")
    private String internetMessageId;//of pending mail of REQUESTEE

    @Column(name = "REPLY_TO")
    private String replyTo;

    @Column(name = "CREATED_TIME")
    private Date createdTime;

    @Column(name = "STATUS")
    private String status;//PENDING, APPROVED, ERROR, WAITING

    @Column(name = "APPROVED_BY")
    private String approvedBy;

    @Column(name = "BUILD_PARAMETERS", columnDefinition = "TEXT")
    private String buildParameters;

    @Column(name = "APPLICATION_NAME")
    private String applicationName;

    @Column(name = "ERROR", columnDefinition = "TEXT")
    private String error;

    @Column(name = "QUEUE_URL")
    private String queueUrl;

    @Column(name = "BUILD_URL")
    private String buildUrl;

    @Column(name = "DEPLOYED_DATE")
    private Date deployedDate;

    @Column(name = "BLUE_OCEAN_URL")
    private String blueOceanUrl;

}
