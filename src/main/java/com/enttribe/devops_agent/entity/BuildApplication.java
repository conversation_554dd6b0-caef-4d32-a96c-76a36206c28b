package com.enttribe.devops_agent.entity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
@Table(name = "BUILD_APPLICATION")
public class BuildApplication {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer id;

    @Column(name = "APPLICATION_NAME", length = 100)
    private String applicationName;

    @Column(name = "ENVIRONMENT", length = 50)
    private String environment;

    @Column(name = "TYPE", length = 20)
    private String type;

    @Column(name = "APPROVER_EMAILS", length = 1000)
    private String approverEmails;

    @Column(name = "ALLOWED_DEPLOYMENT")
    private Integer allowedDeployment;

}
