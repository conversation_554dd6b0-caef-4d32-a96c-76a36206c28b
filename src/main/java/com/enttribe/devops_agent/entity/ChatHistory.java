package com.enttribe.devops_agent.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * Entity for storing chat history between users and the assistant
 */
@Entity
@Getter
@Setter
@Table(name = "CHAT_HISTORY")
public class ChatHistory {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;

    @Column(name = "CONVERSATION_ID", length = 255)
    private String conversationId; // Grouping identifier

}
