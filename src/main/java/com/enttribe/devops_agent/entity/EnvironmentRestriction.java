package com.enttribe.devops_agent.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * Entity to store environment-specific restrictions for build approvals.
 * This allows configuring which environments have restricted access and who can approve them.
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "ENVIRONMENT_RESTRICTION")
public class EnvironmentRestriction {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer id;

    @Column(name = "ENVIRONMENT", nullable = false)
    private String environment;

    @Column(name = "RESTRICTION_PATTERN")
    private String restrictionPattern;

    @Column(name = "ALLOWED_APPROVERS", columnDefinition = "TEXT")
    private String allowedApprovers;

    @Column(name = "ENABLE_RESTRICTION")
    private Boolean enableRestriction = true;

    @Column(name = "RESTRICTION_MESSAGE", columnDefinition = "TEXT")
    private String restrictionMessage;

    @Column(name = "CREATED_DATE")
    private Date createdDate;

    @Column(name = "UPDATED_DATE")
    private Date updatedDate;

}
