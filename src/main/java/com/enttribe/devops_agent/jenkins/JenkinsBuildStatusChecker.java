package com.enttribe.devops_agent.jenkins;


import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

public class JenkinsBuildStatusChecker {

    private static final Logger log = LoggerFactory.getLogger(JenkinsBuildStatusChecker.class);
    private final JenkinsApiClient jenkinsClient;

    public JenkinsBuildStatusChecker(JenkinsApiClient client) {
        this.jenkinsClient = client;
    }

    public String checkBuildStatus(String blueOceanUrl) throws IOException {

        String response = jenkinsClient.sendGetRequest(blueOceanUrl);
        log.debug("response from blueOceanUrl : {}", blueOceanUrl);

        JSONObject json = new JSONObject();
        try {
            json = new JSONObject(response);
        } catch (JSONException e) {
            log.error("unable to parse json : {}", response);
        }

        String state = json.optString("state", "UNKNOWN");
        String result = json.optString("result", "UNKNOWN");
        log.debug("state : {} / result : {}", state, result);

        if ("FINISHED".equalsIgnoreCase(state)) {
            return "SUCCESS".equalsIgnoreCase(result) ? "Build Successful!" : getBuildLogs(blueOceanUrl, json);
        } else {
            return null;
        }

    }

    public String getBuildLogs(String blueOceanUrl) throws IOException {
        String response = jenkinsClient.sendGetRequest(blueOceanUrl);
        log.debug("fetching logs from blueOceanUrl : {}", blueOceanUrl);

        JSONObject json = new JSONObject();
        try {
            json = new JSONObject(response);
        } catch (JSONException e) {
            log.error("unable to parse json : {}", response);
        }

        String state = json.optString("state", "UNKNOWN");
        String result = json.optString("result", "UNKNOWN");
        log.debug("state : {} / result : {}", state, result);
        if ("FINISHED".equalsIgnoreCase(state)) {
            return getBuildLogs(blueOceanUrl, json);
        } else {
            return "";
        }
    }

    private String getBuildLogs(String blueOceanUrl, JSONObject json) throws IOException {
        String logUrl = json.getJSONObject("_links").getJSONObject("log").getString("href");
        String fullLogUrl = extractJenkinsHostUrl(blueOceanUrl) + logUrl;
        log.debug("failure logs url : {}", fullLogUrl);
        return jenkinsClient.sendGetRequest(fullLogUrl);
    }

    private String extractJenkinsHostUrl(String blueOceanUrl) {
        int index = blueOceanUrl.indexOf("/blue/rest/organizations");
        return blueOceanUrl.substring(0, index);
    }

}
