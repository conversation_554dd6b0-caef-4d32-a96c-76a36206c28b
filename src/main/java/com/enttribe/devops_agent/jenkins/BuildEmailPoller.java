package com.enttribe.devops_agent.jenkins;

import com.enttribe.devops_agent.constants.BuildConstants;
import com.enttribe.devops_agent.dao.BuildEmailDao;
import com.enttribe.devops_agent.dao.ProductionBuildDao;
import com.enttribe.devops_agent.dto.UserEmailDto;
import com.enttribe.devops_agent.entity.BuildEmails;
import com.enttribe.devops_agent.entity.ProductionBuild;
import com.enttribe.devops_agent.service.BuildApprovalService;
import com.enttribe.devops_agent.service.BuildEmailProcessingService;
import com.enttribe.devops_agent.service.BuildStatusService;
import com.enttribe.devops_agent.service.EmailPollingService;
import com.enttribe.devops_agent.service.EnvironmentDeploymentConfigService;
import com.enttribe.devops_agent.service.JenkinsService;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Service for polling and processing build emails.
 * This class coordinates the email polling, processing, and build status checking.
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BuildEmailPoller {

    private final BuildEmailDao buildEmailDao;
    private final ProductionBuildDao productionBuildDao;
    private final EmailPollingService emailPollingService;
    private final BuildEmailProcessingService emailProcessingService;
    private final BuildApprovalService buildApprovalService;
    private final BuildStatusService buildStatusService;
    private final JenkinsService jenkinsService;
    private final EnvironmentDeploymentConfigService environmentDeploymentConfigService;

    @Value("${should.poll:false}")
    private Boolean shouldPoll;

    /**
     * Poll for build emails at regular intervals
     */
    @Scheduled(fixedDelay = 2 * 60 * 1000, initialDelay = 30000)
    public void pollBuildEmail() {
        if (!shouldPoll) {
            log.info("Polling is disabled. Skipping build email polling.");
            return;
        }

        log.info("Started polling build emails");
        List<UserEmailDto> emails = emailPollingService.getBuildEmails();
        log.info("Total {} build emails are being polled at {}", emails.size(), java.time.LocalDateTime.now());

        for (UserEmailDto dto : emails) {
            log.info("Polling build email with subject: {}", dto.getSubject());

            // Check if email already exists
            BuildEmails buildEmails = buildEmailDao.getBuildEmailByInternetMessageId(dto.getInternetMessageId());
            if (buildEmails == null) {
                buildEmails = new BuildEmails();
            }

            // Process the email using the existing entity
            buildEmails = emailProcessingService.processEmail(dto, buildEmails);

            // Handle approver emails
            if (buildEmails.getType() != null && buildEmails.getType().equalsIgnoreCase(BuildConstants.APPROVER)) {
                buildApprovalService.processApprovalEmail(dto, buildEmails);
            }

            // Save the email
            buildEmailDao.save(buildEmails);
            log.debug("Build email successfully saved. Subject: {}", buildEmails.getSubject());
        }
    }

    /**
     * Check the status of production builds at regular intervals
     */
    @Scheduled(fixedDelay = 60 * 1000, initialDelay = 60000)
    public void checkProductionBuildStatus() {
        log.info("Started checking build status of production builds");
        List<ProductionBuild> waitingBuildEmails = productionBuildDao.getWaitingProductionBuild();

        for (ProductionBuild waitingEmail : waitingBuildEmails) {
            try {
                // Extract build parameters
                String buildParameters = waitingEmail.getBuildParameters();
                JSONObject buildParams = new JSONObject(buildParameters);
                String environment = buildParams.optString("ENV", null);

                // Check build status
                String buildStatus = jenkinsService.checkBuildStatus(environment, waitingEmail.getBlueOceanUrl());
                if (buildStatus == null) {
                    log.info("Build is still not finished for production for application: {}", waitingEmail.getApplicationName());
                    continue;
                }

                // Create variable map for email templates
                Map<String, Object> variableMap = buildStatusService.createBuildVariableMap(
                        waitingEmail.getBuildEmail(), waitingEmail.getBuildUrl());

                // Process build result
                if (buildStatus.contains("Build Successful!")) {
                    // Process successful build
                    waitingEmail.setStatus(BuildConstants.APPROVED);
                    waitingEmail.setDeployedDate(new Date());
                    buildStatusService.processSuccessfulBuild(
                            waitingEmail.getBuildEmail(), environment, variableMap);
                } else {
                    // Process failed build
                    waitingEmail.setStatus(BuildConstants.ERROR);
                    buildStatusService.processFailedBuild(
                            waitingEmail.getBuildEmail(), buildStatus, environment, variableMap);
                }

            } catch (Exception e) {
                log.error("Error in Jenkins pipeline: {}", e.getMessage(), e);
                waitingEmail.setStatus(BuildConstants.ERROR);
                waitingEmail.setError(e.getMessage());
                emailProcessingService.sendUnexpectedErrorMail(waitingEmail.getReplyTo(), waitingEmail.getInternetMessageId());
            }

            productionBuildDao.save(waitingEmail);
        }
    }

    /**
     * Check the status of regular builds at regular intervals
     */
    @Scheduled(fixedDelay = 60 * 1000, initialDelay = 3000)
    public void checkBuildStatus() {
        log.info("Started checking build status");
        List<BuildEmails> waitingBuildEmails = buildEmailDao.getWaitingBuildEmails();

        for (BuildEmails waitingEmail : waitingBuildEmails) {
            try {
                // Extract build parameters
                String buildParameters = waitingEmail.getBuildParameters();
                JSONObject buildParams = new JSONObject(buildParameters);
                String environment = buildParams.optString("ENV", null);

                // Check build status
                String buildStatus = jenkinsService.checkBuildStatus("dev", waitingEmail.getBlueOceanUrl());
                if (buildStatus == null) {
                    log.info("Build is still not finished for application: {}", waitingEmail.getApplicationName());
                    continue;
                }

                // Create variable map for email templates
                Map<String, Object> variableMap = buildStatusService.createBuildVariableMap(
                        waitingEmail, waitingEmail.getBuildUrl());

                try {
                    String logs = jenkinsService.getBuildLogs("dev", waitingEmail.getBlueOceanUrl());
                    String mainJs = extractMainJsFilename(logs);
                    log.debug("main.js for application {} : {}", waitingEmail.getApplicationName(), mainJs);
                    waitingEmail.setMainJs(mainJs);
                } catch (Exception e) {
                    log.error("exception in extracting main.js : {}", e.getMessage());
                }
                // Check if this is a production build
                Boolean forProduction = waitingEmail.getIsProductionJob();
                if (forProduction) {
                    // Create production build record
                    buildStatusService.createProductionBuild(waitingEmail);
                }

                // Process build result
                if (buildStatus.contains("Build Successful!") && !forProduction) {
                    // Process successful non-production build
                    buildStatusService.processSuccessfulBuild(waitingEmail, environment, variableMap);
                } else if (buildStatus.contains("Build Successful!") && forProduction) {
                    // For production builds that succeed in the first stage, check deployment window for second stage
                    waitingEmail.setStatus(BuildConstants.APPROVED);
                    ProductionBuild productionBuild = productionBuildDao.getProductionBuildByInternetMessageId(
                            waitingEmail.getInternetMessageId());

                    // Check if deployment is allowed at current time for production environment
                    if (!environmentDeploymentConfigService.isDeploymentAllowed(environment)) {
                        log.info("Production deployment not allowed at current time for environment: {}. Queueing production build for later processing.", environment);
                        productionBuild.setStatus(BuildConstants.QUEUED);
                        productionBuild.setApprovedBy(waitingEmail.getApprovedBy());
                        productionBuildDao.save(productionBuild);
                    } else {
                        try {
                            // Determine job type and shell app status
                            String jobFor = waitingEmail.getSubject().toLowerCase().contains("frontend") ? "frontend" : "backend";
                            boolean isShellApp = false;
                            boolean isKeycloakApp = false;
                            if (jobFor.equals("frontend")) {
                                isShellApp = buildParams.optBoolean("IS_SHELL_APP", false);
                            } else {
                                isKeycloakApp = buildParams.optBoolean("IS_KEYCLOAK_APP", false);
                            }

                            // Trigger Jenkins build for production
                            JenkinsService.BuildJobResult buildResult = jenkinsService.triggerJenkinsBuild(
                                    environment, jobFor, buildParams, true, false, isShellApp, isKeycloakApp);

                            // Update production build with job details
                            productionBuild.setQueueUrl(buildResult.queueUrl());
                            productionBuild.setBuildUrl(buildResult.buildUrl());
                            productionBuild.setStatus(BuildConstants.WAITING);
                            productionBuild.setBlueOceanUrl(buildResult.blueOceanUrl());
                            productionBuild.setApprovedBy(waitingEmail.getApprovedBy());

                        } catch (Exception e) {
                            log.error("Error in Jenkins pipeline: {}", e.getMessage(), e);
                            productionBuild.setStatus(BuildConstants.ERROR);
                            productionBuild.setError(e.getMessage());
                            emailProcessingService.sendUnexpectedErrorMail(
                                    productionBuild.getReplyTo(), productionBuild.getInternetMessageId());
                        }

                        productionBuildDao.save(productionBuild);
                    }
                } else {
                    // Process failed build
                    ProductionBuild productionBuild = productionBuildDao.getProductionBuildByInternetMessageId(
                            waitingEmail.getInternetMessageId());
                    if (productionBuild != null) {
                        productionBuild.setStatus(BuildConstants.ERROR);
                        productionBuildDao.save(productionBuild);
                    }

                    buildStatusService.processFailedBuild(waitingEmail, buildStatus, environment, variableMap);
                }

            } catch (Exception e) {
                log.error("Error in Jenkins pipeline: {}", e.getMessage(), e);
                waitingEmail.setStatus(BuildConstants.ERROR);
                waitingEmail.setError(e.getMessage());
                emailProcessingService.sendUnexpectedErrorMail(
                        waitingEmail.getMessageId(), waitingEmail.getInternetMessageId());
            }

            buildEmailDao.save(waitingEmail);
        }
    }

    /**
     * Process queued builds when their deployment window opens
     */
    @Scheduled(fixedDelay = 5 * 60 * 1000, initialDelay = 2 * 60 * 1000) // Run every 5 minutes
    public void processQueuedBuilds() {
        if (!shouldPoll) {
            log.debug("Polling is disabled. Skipping queued build processing.");
            return;
        }

        log.info("Started processing queued builds");

        // Process queued regular builds (non-production builds only, as production builds are queued at second stage)
        List<BuildEmails> queuedBuildEmails = buildEmailDao.getQueuedBuildEmails();
        log.info("Found {} queued build emails to process", queuedBuildEmails.size());

        for (BuildEmails queuedEmail : queuedBuildEmails) {
            try {
                // Extract build parameters
                String buildParameters = queuedEmail.getBuildParameters();
                JSONObject buildParams = new JSONObject(buildParameters);
                String environment = buildParams.optString("ENV", null);

                if (environment == null) {
                    log.error("No environment found for queued build: {}", queuedEmail.getSubject());
                    continue;
                }

                // Only process non-production builds here (production builds are handled separately)
                if (!queuedEmail.getIsProductionJob()) {
                    // Check if deployment is now allowed
                    if (environmentDeploymentConfigService.isDeploymentAllowed(environment)) {
                        log.info("Deployment window is now open for environment: {}. Processing queued build.", environment);
                        processQueuedBuild(queuedEmail, buildParams, environment);
                    } else {
                        log.debug("Deployment window still closed for environment: {}. Keeping build in queue.", environment);
                    }
                } else {
                    log.warn("Found production build in regular queue. This should not happen. Build: {}", queuedEmail.getSubject());
                }

            } catch (Exception e) {
                log.error("Error processing queued build: {}", e.getMessage(), e);
                queuedEmail.setStatus(BuildConstants.ERROR);
                queuedEmail.setError(e.getMessage());
                buildEmailDao.save(queuedEmail);
            }
        }

        // Process queued production builds
        List<ProductionBuild> queuedProductionBuilds = productionBuildDao.getQueuedProductionBuild();
        log.info("Found {} queued production builds to process", queuedProductionBuilds.size());

        for (ProductionBuild queuedBuild : queuedProductionBuilds) {
            try {
                // Extract build parameters
                String buildParameters = queuedBuild.getBuildParameters();
                JSONObject buildParams = new JSONObject(buildParameters);
                String environment = buildParams.optString("ENV", null);

                if (environment == null) {
                    log.error("No environment found for queued production build: {}", queuedBuild.getApplicationName());
                    continue;
                }

                // Check if deployment is now allowed
                if (environmentDeploymentConfigService.isDeploymentAllowed(environment)) {
                    log.info("Deployment window is now open for environment: {}. Processing queued production build.", environment);
                    processQueuedProductionBuild(queuedBuild, buildParams, environment);
                } else {
                    log.debug("Deployment window still closed for environment: {}. Keeping production build in queue.", environment);
                }

            } catch (Exception e) {
                log.error("Error processing queued production build: {}", e.getMessage(), e);
                queuedBuild.setStatus(BuildConstants.ERROR);
                queuedBuild.setError(e.getMessage());
                productionBuildDao.save(queuedBuild);
            }
        }
    }

    /**
     * Process a queued regular build
     */
    private void processQueuedBuild(BuildEmails queuedEmail, JSONObject buildParams, String environment) {
        boolean forProduction = queuedEmail.getIsProductionJob();

        try {
            // Determine job type and shell app status
            String jobFor = queuedEmail.getSubject().toLowerCase().contains("frontend") ? "frontend" : "backend";
            boolean isShellApp = false;
            boolean isKeycloakApp = false;
            if (jobFor.equals("frontend")) {
                isShellApp = buildParams.optBoolean("IS_SHELL_APP", false);
            } else {
                isKeycloakApp = buildParams.optBoolean("IS_KEYCLOAK_APP", false);
            }

            // Trigger Jenkins build
            JenkinsService.BuildJobResult buildResult = jenkinsService.triggerJenkinsBuild(
                    environment, jobFor, buildParams, forProduction, true, isShellApp, isKeycloakApp);

            // Update build email with job details
            queuedEmail.setQueueUrl(buildResult.queueUrl());
            queuedEmail.setBuildUrl(buildResult.buildUrl());
            queuedEmail.setStatus(BuildConstants.WAITING);
            queuedEmail.setBlueOceanUrl(buildResult.blueOceanUrl());

            buildEmailDao.save(queuedEmail);
            log.info("Successfully triggered queued build for application: {} in environment: {}",
                    queuedEmail.getApplicationName(), environment);

        } catch (Exception e) {
            log.error("Error triggering queued build: {}", e.getMessage(), e);
            queuedEmail.setStatus(BuildConstants.ERROR);
            queuedEmail.setError(e.getMessage());
            buildEmailDao.save(queuedEmail);
        }
    }

    private static String extractMainJsFilename(String logs) {
        // Regex: match 'main.' followed by anything until '.js', bounded by '*'
        String pattern = "\\*+\\s*(main\\.[^\\s*]+\\.js)\\s*\\*+";
        Matcher matcher = Pattern.compile(pattern).matcher(logs);

        if (matcher.find()) {
            return matcher.group(1);  // group(1) is the filename
        }
        return "---";
    }

    /**
     * Process a queued production build
     */
    private void processQueuedProductionBuild(ProductionBuild queuedBuild, JSONObject buildParams, String environment) {
        try {
            // Determine job type and shell app status
            String jobFor = queuedBuild.getBuildEmail().getSubject().toLowerCase().contains("frontend") ? "frontend" : "backend";
            boolean isShellApp = false;
            boolean isKeycloakApp = false;
            if (jobFor.equals("frontend")) {
                isShellApp = buildParams.optBoolean("IS_SHELL_APP", false);
            } else {
                isKeycloakApp = buildParams.optBoolean("IS_KEYCLOAK_APP", false);
            }

            // Trigger Jenkins build for production
            JenkinsService.BuildJobResult buildResult = jenkinsService.triggerJenkinsBuild(
                    environment, jobFor, buildParams, true, false, isShellApp, isKeycloakApp);

            // Update production build with job details
            queuedBuild.setQueueUrl(buildResult.queueUrl());
            queuedBuild.setBuildUrl(buildResult.buildUrl());
            queuedBuild.setStatus(BuildConstants.WAITING);
            queuedBuild.setBlueOceanUrl(buildResult.blueOceanUrl());

            productionBuildDao.save(queuedBuild);
            log.info("Successfully triggered queued production build for application: {} in environment: {}",
                    queuedBuild.getApplicationName(), environment);

        } catch (Exception e) {
            log.error("Error triggering queued production build: {}", e.getMessage(), e);
            queuedBuild.setStatus(BuildConstants.ERROR);
            queuedBuild.setError(e.getMessage());
            productionBuildDao.save(queuedBuild);
        }
    }


    /**
     * Backend parameters record for AI extraction
     */
    public record BackendParameters(
            @JsonPropertyDescription("This is mail type: it can either be APPROVER or REQUESTEE") String TYPE,
            @JsonPropertyDescription("True if approved by APPROVER, else false") Boolean APPROVED,
            @JsonPropertyDescription("The environment for the build to deploy") String ENV,
            String APPLICATION_NAME,
            String K8S_NAME_SPACE,
            @JsonPropertyDescription("True if provided true, default is false") boolean SKIP_TEST_CASES,
            @JsonPropertyDescription("True if provided true, default is false") boolean SKIP_OBFUSCATION,
            @JsonPropertyDescription("True if provided true, default is false") boolean SKIP_JAVADOC,
            @JsonPropertyDescription("True if provided true, default is false") boolean SKIP_DEPENDENCY_CHECK,
            String GIT_TAG,
            String GIT_BRANCH,
            String GIT_REPO_URL,
            String DOCKER_REGISTRY,
            @JsonPropertyDescription("True if provided true, default is false") boolean UPGRADE_DATABASE,
            @JsonPropertyDescription("True if provided true, default is false") boolean IS_KEYCLOAK_APP
    ) {
    }

    /**
     * Frontend parameters record for AI extraction
     */
    public record FrontendParameters(
            @JsonPropertyDescription("This is mail type: it can either be APPROVER or REQUESTEE") String TYPE,
            @JsonPropertyDescription("True if approved by APPROVER, else false") Boolean APPROVED,
            @JsonPropertyDescription("The environment for the build to deploy") String ENV,
            String APPLICATION_NAME,
            String PROJECT,
            String K8S_NAME_SPACE,
            String GIT_TAG,
            String GIT_BRANCH,
            String GIT_REPO_URL,
            String DOCKER_REGISTRY,
            @JsonPropertyDescription("True if provided, default is false") Boolean IS_SHELL_APP
    ) {
    }

}
