package com.enttribe.devops_agent.jenkins;


import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

public class JenkinsQueueChecker {

    private static final Logger log = LoggerFactory.getLogger(JenkinsQueueChecker.class);
    private final JenkinsApiClient jenkinsClient;

    public JenkinsQueueChecker(JenkinsApiClient client) {
        this.jenkinsClient = client;
    }

    public String waitForBuildToStart(String queueUrl) throws IOException, InterruptedException {
        while (true) {
            String response = jenkinsClient.sendGetRequest(queueUrl + "/api/json");
            JSONObject json = null;
            try {
                json = new JSONObject(response);
            } catch (JSONException e) {
                log.error("unable to parse json : {}", response);
                throw e;
            }

            if (json.has("executable")) {
                return json.getJSONObject("executable").getString("url");
            }

            log.debug("Waiting for job to start...");
            TimeUnit.SECONDS.sleep(2);
        }
    }

    public String waitForBuildByTriggerId(String jenkinsUrl, String jobName, String triggerId) throws IOException, InterruptedException {
        String jobApiUrl = jenkinsUrl + "/job/" + jobName + "/api/json?tree=builds[number,url,actions[parameters[name,value]]]";

        for (int i = 0; i < 30; i++) { // wait up to ~1 min (30 * 2)
            String response = jenkinsClient.sendGetRequest(jobApiUrl);

            JSONObject jobJson = new JSONObject(response);
            JSONArray builds = jobJson.getJSONArray("builds");

            for (int j = 0; j < builds.length(); j++) {
                JSONObject build = builds.getJSONObject(j);
                JSONArray actions = build.getJSONArray("actions");

                for (int k = 0; k < actions.length(); k++) {
                    JSONObject action = actions.optJSONObject(k);
                    if (action == null || !action.has("parameters")) continue;

                    JSONArray params = action.getJSONArray("parameters");
                    for (int p = 0; p < params.length(); p++) {
                        JSONObject param = params.getJSONObject(p);

                        if ("TRIGGER_ID".equals(param.getString("name")) &&
                                triggerId.equals(param.getString("value"))) {
                            log.info("found the value : {}", param);
                            return build.getString("url");
                        }
                    }
                }
            }

            Thread.sleep(2000);
        }

        throw new RuntimeException("Timed out waiting for build with TRIGGER_ID=" + triggerId);
    }

}
