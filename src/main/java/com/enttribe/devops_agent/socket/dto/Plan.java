package com.enttribe.devops_agent.socket.dto;

import lombok.*;

import java.util.function.Function;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Plan {
    private String message; // Message to show to user (optional, can be null)
    private String command; // Shell command to run (optional)
    private boolean askConfirmation; // Should we pause for user input?
    private Function<String, Plan> nextStepResolver; // Based on command result, return next Plan

    public boolean hasNextStep() {
        return nextStepResolver != null;
    }

    public Plan resolveNext(String output) {
        return nextStepResolver != null ? nextStepResolver.apply(output) : null;
    }
}
