package com.enttribe.devops_agent.socket;

import com.enttribe.commons.ai.chat.AiChatModel;
import com.enttribe.commons.ai.util.JsonUtils;
import com.enttribe.devops_agent.handler.FetchLogsIntentHandler;
import com.enttribe.devops_agent.socket.dto.Plan;
import com.enttribe.devops_agent.socket.dto.SocketRequest;
import com.enttribe.devops_agent.socket.dto.SocketResponse;
import com.enttribe.devops_agent.util.Tools;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@RequiredArgsConstructor
@Component
public class LLMWebSocketHandler extends TextWebSocketHandler {

    private final Tools tools;
    private final FetchLogsIntentHandler fetchLogsIntentHandler = new FetchLogsIntentHandler();

    private final Map<String, WebSocketSession> sessions = new ConcurrentHashMap<>();
    private final Map<String, CompletableFuture<String>> pendingConfirmations = new ConcurrentHashMap<>();
    private final Map<String, Plan> sessionPlanState = new ConcurrentHashMap<>();

    private final AiChatModel aiChatModel;

    @Override
    public void afterConnectionEstablished(WebSocketSession session) {
        sessions.put(session.getId(), session);
    }

    @Override
    public void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        SocketRequest request = parseSocketRequest(message);
        String sessionId = session.getId();

        switch (request.getType()) {
            case "CONFIRMATION" -> {
                CompletableFuture<String> future = pendingConfirmations.remove(sessionId);
                if (future != null) {
                    future.complete(request.getData());
                }
            }
            default -> {
                //                String podName = "prompt-analyzer"; // Normally from LLM
                String podName = "nginx"; // Normally from LLM
                Plan plan = fetchLogsIntentHandler.buildPlan(podName);
                executePlan(session, plan);
            }
        }
    }

    private void executePlan(WebSocketSession session, Plan plan) throws IOException {
        String sessionId = session.getId();
        sessionPlanState.put(sessionId, plan);

        if (plan.getMessage() != null) {
            sendSocketResponse(session, SocketResponse.builder()
                    .type(plan.isAskConfirmation() ? "CONFIRMATION" : "CHAT")
                    .data(plan.getMessage())
                    .build());
        }

        if (plan.getCommand() != null) {
            List<String> output = tools.executeCommand(plan.getCommand());
            String result = String.join("\n", output);
            log.info("Command result: {}", result);

            if (plan.hasNextStep()) {
                executePlan(session, plan.resolveNext(result));
            }
        } else if (plan.isAskConfirmation()) {
            CompletableFuture<String> future = new CompletableFuture<>();
            pendingConfirmations.put(sessionId, future);
            future.thenAccept(response -> {
                try {
                    executePlan(session, plan.resolveNext(response));
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            });
        }
    }

    private void sendSocketResponse(WebSocketSession session, SocketResponse response) throws IOException {
        String json = JsonUtils.convertToJSON(response);
        session.sendMessage(new TextMessage(json));
    }

    private SocketRequest parseSocketRequest(TextMessage message) {
        try {
            return JsonUtils.convertJsonToObject(message.getPayload(), SocketRequest.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Invalid socket request", e);
        }
    }
}
