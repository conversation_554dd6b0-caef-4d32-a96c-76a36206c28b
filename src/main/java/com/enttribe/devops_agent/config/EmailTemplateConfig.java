package com.enttribe.devops_agent.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;

import java.util.List;

@Configuration
@ConfigurationProperties(prefix = "email")
@Getter
@Setter
public class EmailTemplateConfig {

    private Resource successDraft;
    private Resource failedDraft;
    private Resource formatIssue;
    private List<String> approvers;
    private String limitedDeploymentTemplate;
    private String unauthorizedTemplate;
    private String unexpectedErrorTemplate;
}
