package com.enttribe.devops_agent.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Configuration
@ConfigurationProperties(prefix = "jenkins")
@Getter
@Setter
public class JenkinsConfig {

    private Map<String, JobDetails> environments = new HashMap<>();
    private List<String> supportedEnvironments;
    private String emailId;

    @Getter
    @Setter
    public static class JobDetails {
        private String jenkinsUrl;
        private String frontendJobName;
        private String backendJobName;
        private String shellAppJobName;
        private String keycloakJobName;
        private String jenkinsUser;
        private String jenkinsToken;
    }

}
