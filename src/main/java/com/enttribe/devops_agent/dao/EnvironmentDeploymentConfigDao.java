package com.enttribe.devops_agent.dao;

import com.enttribe.devops_agent.entity.EnvironmentDeploymentConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Repository interface for EnvironmentDeploymentConfig entity.
 * Provides methods to manage deployment time window configurations.
 */
@Repository
public interface EnvironmentDeploymentConfigDao extends JpaRepository<EnvironmentDeploymentConfig, Integer> {

    /**
     * Find deployment configuration by environment name
     *
     * @param environment The environment name
     * @return Optional containing the configuration if found
     */
    @Query("SELECT edc FROM EnvironmentDeploymentConfig edc WHERE edc.environment = :environment AND edc.isActive = true")
    Optional<EnvironmentDeploymentConfig> findByEnvironment(@Param("environment") String environment);

    /**
     * Check if deployment configuration exists for an environment
     *
     * @param environment The environment name
     * @return true if configuration exists and is active
     */
    @Query("SELECT COUNT(edc) > 0 FROM EnvironmentDeploymentConfig edc WHERE edc.environment = :environment AND edc.isActive = true")
    boolean existsByEnvironment(@Param("environment") String environment);
}
