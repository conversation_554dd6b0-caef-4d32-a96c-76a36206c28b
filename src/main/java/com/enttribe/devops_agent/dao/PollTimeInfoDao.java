package com.enttribe.devops_agent.dao;

import com.enttribe.devops_agent.entity.PollTimeInfo;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

public interface PollTimeInfoDao extends JpaRepository<PollTimeInfo, Integer> {

    Optional<PollTimeInfo> findFirstByEmailAndStatusOrderByEndTimeDesc(String email, PollTimeInfo.Status status);

    Optional<PollTimeInfo> findFirstByEmailOrderByStartTimeDesc(String email);


}
