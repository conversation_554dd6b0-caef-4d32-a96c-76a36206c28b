package com.enttribe.devops_agent.dao;

import com.enttribe.devops_agent.entity.ChatMessage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ChatMessageDao extends JpaRepository<ChatMessage, Long> {

    @Query("select u from ChatMessage u where u.conversationId = ?1")
    List<ChatMessage> getChatHistory(String conversationId);
}
