package com.enttribe.devops_agent.dao;


import com.enttribe.devops_agent.entity.BuildApplication;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface BuildApplicationDao extends JpaRepository<BuildApplication, Integer> {

    @Query("SELECT ba FROM BuildApplication ba WHERE ba.applicationName = :applicationName AND ba.environment = :environment and ba.type = :type")
    BuildApplication findByApplicationName(String applicationName, String environment,String type);

    @Query("SELECT ba.approverEmails FROM BuildApplication ba WHERE ba.applicationName = :applicationName and ba.type = :type")
    String getListOfApproveEmails(String applicationName,String type);

}
