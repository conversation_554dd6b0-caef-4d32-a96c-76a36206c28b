package com.enttribe.devops_agent.dao;


import com.enttribe.devops_agent.entity.BuildControl;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface BuildControlDao extends JpaRepository<BuildControl, Integer> {

    @Query("SELECT bc.successCount FROM BuildControl bc WHERE bc.applicationName = :applicationName AND bc.deploymentDate = :deploymentDate AND bc.environment = :environment and bc.type = :type")
    Integer findSuccessDeploymentCountByDate(
            @Param("applicationName") String applicationName,
            @Param("deploymentDate") LocalDate deploymentDate,
            @Param("environment") String environment,
            @Param("type") String type
    );

    @Query("SELECT bc FROM BuildControl bc WHERE bc.applicationName = :applicationName AND bc.deploymentDate = :deploymentDate AND bc.environment = :environment and bc.type = :type")
    List<BuildControl> findByApplicationNameAndDeploymentDate(
            @Param("applicationName") String applicationName,
            @Param("deploymentDate") LocalDate deploymentDate,
            @Param("environment") String environment,
            @Param("type") String type
    );

}
