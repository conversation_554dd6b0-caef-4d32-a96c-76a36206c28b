package com.enttribe.devops_agent.dao;

import com.enttribe.devops_agent.entity.EnvironmentRestriction;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for EnvironmentRestriction entity.
 * Provides methods to manage environment-specific build restrictions.
 */
@Repository
public interface EnvironmentRestrictionDao extends JpaRepository<EnvironmentRestriction, Integer> {

    /**
     * Find all active environment restrictions
     *
     * @return List of active restrictions
     */
    @Query("SELECT er FROM EnvironmentRestriction er WHERE er.enableRestriction = true")
    List<EnvironmentRestriction> findAllActiveRestrictions();

}
