package com.enttribe.devops_agent.dao;

import com.enttribe.devops_agent.entity.ChatHistory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ChatHistoryDao extends JpaRepository<ChatHistory, Long> {


    @Query("select u from ChatHistory u where u.conversationId = ?1")
    Optional<ChatHistory> getChatHistory(String conversationId);
}
