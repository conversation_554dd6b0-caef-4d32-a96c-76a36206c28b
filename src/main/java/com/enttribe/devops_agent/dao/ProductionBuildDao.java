package com.enttribe.devops_agent.dao;

import com.enttribe.devops_agent.entity.ProductionBuild;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProductionBuildDao extends JpaRepository<ProductionBuild, Integer> {

    @Query("select u from ProductionBuild u where u.internetMessageId = :internetMessageId")
    ProductionBuild getProductionBuildByInternetMessageId(String internetMessageId);

    @Query("select u from ProductionBuild u where u.status = 'WAITING'")
    List<ProductionBuild> getWaitingProductionBuild();

    @Query("select u from ProductionBuild u where u.status = 'QUEUED'")
    List<ProductionBuild> getQueuedProductionBuild();

}