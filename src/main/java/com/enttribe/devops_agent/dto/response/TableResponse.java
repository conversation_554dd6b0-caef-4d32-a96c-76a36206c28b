package com.enttribe.devops_agent.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.json.JSONObject;
import org.json.JSONArray;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TableResponse {

    private String display_type = "table";
    private String title;
    private List<Header> headers = new ArrayList<>();
    private List<Map<String, String>> rows = new ArrayList<>();

    @Override
    public String toString() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("display_type", display_type);
        jsonObject.put("title", title);

        JSONArray headersArray = new JSONArray();
        for (Header header : headers) {
            JSONObject headerObject = new JSONObject();
            headerObject.put("headerName", header.headerName());
            headerObject.put("field", header.field());
            headersArray.put(headerObject);
        }
        jsonObject.put("headers", headersArray);

        JSONArray rowsArray = new JSONArray();
        for (Map<String, String> row : rows) {
            rowsArray.put(new JSONObject(row));
        }
        jsonObject.put("rows", rowsArray);

        return jsonObject.toString(2);
    }
}
