package com.enttribe.devops_agent.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserEmailDto {

    private String from;
    private String subject;
    private String body;
    @JsonIgnore
    private String bodyPreview;
    private String conversationId;
    private Date createdTime;
    private String id;
    private Boolean hasAttachments;
    private String internetMessageId;

}
