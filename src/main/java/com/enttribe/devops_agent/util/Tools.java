package com.enttribe.devops_agent.util;


import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

@Service
public class Tools {

    private static final Logger log = LoggerFactory.getLogger(Tools.class);

    @Tool(description = "Executes a shell command in the terminal and returns the output. Captures both standard output and error streams.")
    public List<String> executeCommand(@JsonPropertyDescription("valid shell command") String command) {

        log.info("the command : {}", command);

//        command = command.replace("kubectl", "kubectl  --kubeconfig=/opt/visionwaves/prompt-analyzer-build/conf");

        List<String> output = new ArrayList<>();
        ProcessBuilder processBuilder = new ProcessBuilder();

        // Use shell for compatibility (Linux/Mac: bash/sh, Windows: cmd)
        processBuilder.command("sh", "-c", command);

        try {
            Process process = processBuilder.start();
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));

            // Read standard output
            String line;
            while ((line = reader.readLine()) != null) {
                output.add(line);
            }

            // Read error output (if any)
            while ((line = errorReader.readLine()) != null) {
                output.add(line);
            }

            process.waitFor(); // Wait for process to complete
        } catch (IOException | InterruptedException e) {
            log.error("error while executing command : {}", e.getMessage(), e);
        }
        return output;
    }

    @Tool(description = "Retrieves logs from a shell command. Use this tool only for getting logs.", returnDirect = true)
    public String getLogs(@JsonPropertyDescription("valid shell command for getting logs") String command) {

        command = command.replace(" -f ", " ");
        log.info("the command for logs : {}", command);

//        command = command.replace("kubectl", "kubectl  --kubeconfig=/opt/visionwaves/prompt-analyzer-build/conf");

        StringBuilder output = new StringBuilder();
        ProcessBuilder processBuilder = new ProcessBuilder();

        // Use shell for compatibility (Linux/Mac: bash/sh, Windows: cmd)
        processBuilder.command("sh", "-c", command);

        try {
            Process process = processBuilder.start();
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));

            // Read standard output
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
            }

            // Read error output (if any)
            while ((line = errorReader.readLine()) != null) {
                output.append(line).append("\n");
            }

            process.waitFor(); // Wait for process to complete
        } catch (IOException | InterruptedException e) {
            log.error("error while executing command : {}", e.getMessage(), e);
        }
        return output.toString().trim();
    }


}
