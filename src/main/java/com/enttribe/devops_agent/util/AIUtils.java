package com.enttribe.devops_agent.util;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.knuddels.jtokkit.Encodings;
import com.knuddels.jtokkit.api.Encoding;
import com.knuddels.jtokkit.api.EncodingRegistry;
import com.knuddels.jtokkit.api.EncodingType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.core.io.Resource;

import java.util.Map;


@Slf4j
public class AIUtils {

    public static String getResolvedPrompt(Resource prompt, Map<String, Object> model) {
        PromptTemplate promptTemplate = PromptTemplate.builder()
                .resource(prompt)
                .variables(model)
                .build();
        return promptTemplate.render();
    }

    public static String convertToJSON(Object object, boolean isObject) {
        try {
            if (object == null) return isObject ? "{}" : "[]";
            ObjectMapper mapper = CommonUtils.getObjectMapper();
            return mapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("Error inside @method convertToJSON", e);
            return object.toString();
        }
    }

    public static String reduceTextToMaxTokens(String text, int maxTokens) {
        // Step 1: Get the initial token count
        int tokenCount = getTokensFromText(text);

        // Step 2: If the token count is already below the limit, return the text as is
        if (tokenCount <= maxTokens) {
            return text;
        }

        // Step 3: Keep reducing the text by half from the end until it's under the maxTokens
        while (tokenCount > maxTokens) {
            // Reduce the text length by half
            text = text.substring(0, text.length() / 2);
            tokenCount = getTokensFromText(text);
        }

        // The loop ends when the token count is <= maxTokens.
        return text;
    }

    public static int getTokensFromText(String text) {
        int tokens = 0;
        try {
            EncodingRegistry registry = Encodings.newDefaultEncodingRegistry();
            Encoding enc = registry.getEncoding(EncodingType.CL100K_BASE);
            tokens = enc.countTokens(text);
        } catch (Exception e) {
            log.error("Error getting token from text", e);
        }
        return tokens;
    }


}
