package com.enttribe.devops_agent.handler;


import com.enttribe.devops_agent.socket.dto.Plan;
import com.enttribe.devops_agent.dto.response.TableResponse;
import com.enttribe.devops_agent.dto.response.Header;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.stream.Collectors;

public class FetchLogsIntentHandler {

    public Plan buildPlan(String podName) {
        return Plan.builder()
                .message("🔍 Looking for pods matching the name: " + podName)
                .command("kubectl get pod -A | awk 'NR==1 || /" + podName + "/'")
                .nextStepResolver(output -> resolveNext(podName, output))
                .build();
    }

    private Plan resolveNext(String podName, String output) {
        List<String> lines = List.of(output.split("\n"));

        if (lines.isEmpty() || (lines.size() == 1 && lines.get(0).isBlank())) {
            return Plan.builder()
                    .message("❌ No pods found for: " + podName)
                    .build();
        }

        // The first line is the header
        String headerLine = lines.get(0);

        List<String> dataLines = lines.subList(1, lines.size());

        if (dataLines.isEmpty()) {
            return Plan.builder()
                    .message("❌ No pods found for: " + podName)
                    .build();
        }

        if (dataLines.size() == 1) {
            String selectedPod = extractPodName(dataLines.get(0));
            return Plan.builder()
                    .message("✅ Found pod: " + selectedPod + "\n📦 Fetching logs...")
                    .command("kubectl logs " + selectedPod)
                    .nextStepResolver(response -> Plan.builder()
                            .message(response).build())
                    .build();
        } else {
            TableResponse tableResponse = new TableResponse();
            String commandUsed = "kubectl get pod -A | awk 'NR==1 || /" + podName + "/'";
            tableResponse.setTitle(commandUsed);

            List<Header> headers = new ArrayList<>();
            headers.add(new Header("NAMESPACE", "namespace"));
            headers.add(new Header("NAME", "name"));
            headers.add(new Header("READY", "ready"));
            headers.add(new Header("STATUS", "status"));
            headers.add(new Header("RESTARTS", "restarts"));
            headers.add(new Header("AGE", "age"));

            tableResponse.setHeaders(headers);

            List<Map<String, String>> rows = dataLines.stream()
                    .map(this::parsePodLine)
                    .collect(Collectors.toList());
            tableResponse.setRows(rows);

            return Plan.builder()
                    .message(tableResponse.toString())
                    .askConfirmation(true)
                    .nextStepResolver(selectedPodName -> {
                        if (selectedPodName != null && !selectedPodName.isBlank()) {
                            return Plan.builder()
                                    .message("📦 Fetching logs for: " + selectedPodName)
                                    .command("kubectl logs " + selectedPodName)
                                    .nextStepResolver(response -> Plan.builder()
                                            .message(response)
                                            .build())
                                    .build();
                        } else {
                            return Plan.builder()
                                    .message("🛑 No pod name provided or invalid selection.")
                                    .build();
                        }
                    })
                    .build();
        }
    }

    private String extractPodName(String line) {
        return line.trim().split("\\s+")[1]; // This is for extracting single pod name for logs
    }

    private Map<String, String> parsePodLine(String line) {
        String[] parts = line.trim().split("\\s+");
        Map<String, String> row = new LinkedHashMap<>();
        if (parts.length >= 6) {
            row.put("namespace", parts[0]);
            row.put("name", parts[1]);
            row.put("ready", parts[2]);
            row.put("status", parts[3]);
            row.put("restarts", parts[4]);
            row.put("age", parts[5]);
        }
        return row;
    }
}